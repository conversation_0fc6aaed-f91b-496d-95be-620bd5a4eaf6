using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;


namespace PEMTestSystem.Services.Devices
{
    /// <summary>
    /// 串口通信管理器
    /// 负责串口资源共享、冲突检测、重连机制和通信质量监测。
    /// 支持多设备共享同一串口，自动处理从站地址冲突检测，提供通信质量统计和健康监测功能。
    /// </summary>
    /// <remarks>
    /// 该管理器采用线程安全设计，支持并发访问。主要功能包括：
    /// 1. 串口资源池管理，避免重复创建串口实例
    /// 2. 设备注册与注销，支持动态添加和移除设备
    /// 3. 从站地址冲突检测，确保同一串口上设备地址唯一
    /// 4. 通信质量监测，统计成功率、响应时间等指标
    /// 5. 自动健康检查和重连机制，提高系统稳定性
    /// </remarks>
    public class SerialPortManager : IDisposable
    {
        /// <summary>
        /// 串口信息字典，以端口名为键，存储串口的详细信息
        /// </summary>
        /// <remarks>
        /// 使用 ConcurrentDictionary 确保线程安全，支持并发读写操作
        /// </remarks>
        private readonly ConcurrentDictionary<string, SerialPortInfo> _serialPorts;

        /// <summary>
        /// 端口设备映射字典，记录每个串口上注册的设备列表
        /// </summary>
        /// <remarks>
        /// 键为端口名，值为该端口上注册的设备ID列表，用于管理设备与端口的关联关系
        /// </remarks>
        private readonly ConcurrentDictionary<string, List<string>> _portDeviceMap;

        /// <summary>
        /// 健康检查定时器，定期检查串口连接状态和通信质量
        /// </summary>
        /// <remarks>
        /// 每30秒执行一次健康检查，检测长时间未使用的端口和通信质量异常的端口
        /// </remarks>
        private readonly Timer _healthCheckTimer;

        /// <summary>
        /// 管理操作同步锁，确保串口管理操作的原子性
        /// </summary>
        /// <remarks>
        /// 使用 SemaphoreSlim 而非 lock 语句，支持异步等待，避免阻塞线程
        /// </remarks>
        private readonly SemaphoreSlim _managementLock;

        /// <summary>
        /// 标识对象是否已被释放，用于防止重复释放资源
        /// </summary>
        private bool _disposed = false;

        /// <summary>
        /// 初始化串口通信管理器
        /// </summary>
        /// <remarks>
        /// 构造函数会初始化所有内部数据结构，启动健康检查定时器，并记录初始化日志。
        /// 健康检查定时器设置为每30秒执行一次，用于监测串口连接状态和通信质量。
        /// </remarks>
        public SerialPortManager()
        {
            _serialPorts = new ConcurrentDictionary<string, SerialPortInfo>();
            _portDeviceMap = new ConcurrentDictionary<string, List<string>>();
            _managementLock = new SemaphoreSlim(1, 1);

            // 每30秒检查一次串口健康状态
            _healthCheckTimer = new Timer(PerformHealthCheck, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));

            App.AlarmService.Info("串口管理", "串口通信管理器初始化完成");
        }

        /// <summary>
        /// 异步注册设备到指定串口
        /// </summary>
        /// <param name="deviceId">设备唯一标识符，用于区分不同的设备实例</param>
        /// <param name="portName">串口名称，如 "COM1"、"COM2" 等</param>
        /// <param name="baudRate">波特率，必须与同一端口上其他设备的波特率一致</param>
        /// <param name="slaveAddress">Modbus从站地址，在同一端口上必须唯一</param>
        /// <returns>
        /// 返回注册结果：
        /// - true: 注册成功
        /// - false: 注册失败（波特率不匹配、从站地址冲突等）
        /// </returns>
        /// <exception cref="ArgumentException">当参数无效时抛出</exception>
        /// <remarks>
        /// 注册过程包括以下检查和操作：
        /// 1. 检查端口是否已存在，不存在则创建新的端口信息
        /// 2. 验证波特率是否与现有设备一致
        /// 3. 检查从站地址是否与其他设备冲突
        /// 4. 将设备添加到端口的设备列表中
        /// 5. 更新设备计数和从站地址映射
        ///
        /// 该方法是线程安全的，使用信号量确保并发访问的安全性。
        /// 如果设备已经注册，会检查参数一致性，一致则返回成功。
        /// </remarks>
        /// <example>
        /// <code>
        /// var manager = new SerialPortManager();
        /// bool success = await manager.RegisterDeviceAsync("Device001", "COM1", 9600, 1);
        /// if (success)
        /// {
        ///     Console.WriteLine("设备注册成功");
        /// }
        /// </code>
        /// </example>
        public async Task<bool> RegisterDeviceAsync(string deviceId, string portName, int baudRate, byte slaveAddress)
        {
            await _managementLock.WaitAsync();
            
            try
            {
                // 检查端口是否已存在
                if (!_serialPorts.ContainsKey(portName))
                {
                    var portInfo = new SerialPortInfo
                    {
                        PortName = portName,
                        BaudRate = baudRate,
                        IsOpen = false,
                        LastAccessTime = DateTime.Now,
                        CommunicationQuality = new CommunicationQuality(),
                        DeviceSlaveAddresses = new Dictionary<string, byte>()
                    };

                    _serialPorts[portName] = portInfo;
                    _portDeviceMap[portName] = new List<string>();
                    
                    App.AlarmService.Info("串口管理", $"注册新串口: {portName}, 波特率: {baudRate}");
                }

                // 检查波特率是否匹配
                var existingPort = _serialPorts[portName];
                if (existingPort.BaudRate != baudRate)
                {
                    App.AlarmService.Error("串口管理", $"设备 {deviceId} 的波特率 {baudRate} 与端口 {portName} 现有波特率 {existingPort.BaudRate} 不匹配");
                    return false;
                }

                // 检查从站地址冲突
                foreach (var kvp in existingPort.DeviceSlaveAddresses)
                {
                    if (kvp.Value == slaveAddress && kvp.Key != deviceId)
                    {
                        App.AlarmService.Error("串口管理", $"设备 {deviceId} 的从站地址 {slaveAddress} 与端口 {portName} 上的设备 {kvp.Key} 冲突");
                        return false;
                    }
                }

                // 检查设备是否已注册
                var devicesOnPort = _portDeviceMap[portName];
                if (devicesOnPort.Contains(deviceId))
                {
                    // 检查从站地址是否一致
                    if (existingPort.DeviceSlaveAddresses.TryGetValue(deviceId, out byte existingSlaveAddress))
                    {
                        if (existingSlaveAddress != slaveAddress)
                        {
                            App.AlarmService.Error("串口管理", $"设备 {deviceId} 的从站地址 {slaveAddress} 与已注册的地址 {existingSlaveAddress} 不匹配");
                            return false;
                        }
                    }
                    App.AlarmService.Warning("串口管理", $"设备 {deviceId} 已在端口 {portName} 上注册");
                    return true;
                }

                // 添加设备到端口
                devicesOnPort.Add(deviceId);
                existingPort.DeviceSlaveAddresses[deviceId] = slaveAddress;
                existingPort.DeviceCount = devicesOnPort.Count;

                App.AlarmService.Info("串口管理", $"设备 {deviceId} 注册到端口 {portName}, 从站地址: {slaveAddress}");
                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("串口管理", $"注册设备 {deviceId} 到端口 {portName} 失败", ex);
                return false;
            }
            finally
            {
                _managementLock.Release();
            }
        }

        /// <summary>
        /// 异步注销指定设备从串口
        /// </summary>
        /// <param name="deviceId">要注销的设备唯一标识符</param>
        /// <param name="portName">设备所在的串口名称</param>
        /// <returns>
        /// 返回注销结果：
        /// - true: 注销成功
        /// - false: 注销失败（设备不存在或端口不存在）
        /// </returns>
        /// <remarks>
        /// 注销过程包括以下操作：
        /// 1. 从端口的设备列表中移除指定设备
        /// 2. 清除设备的从站地址映射记录
        /// 3. 更新端口的设备计数
        /// 4. 如果端口上没有设备，自动关闭并移除端口
        /// 5. 释放相关的串口资源
        ///
        /// 该方法是线程安全的，使用信号量确保并发访问的安全性。
        /// 当端口上的最后一个设备被注销时，会自动关闭串口连接并释放资源。
        /// </remarks>
        /// <example>
        /// <code>
        /// var manager = new SerialPortManager();
        /// bool success = await manager.UnregisterDeviceAsync("Device001", "COM1");
        /// if (success)
        /// {
        ///     Console.WriteLine("设备注销成功");
        /// }
        /// </code>
        /// </example>
        public async Task<bool> UnregisterDeviceAsync(string deviceId, string portName)
        {
            await _managementLock.WaitAsync();
            
            try
            {
                if (_portDeviceMap.TryGetValue(portName, out var devices))
                {
                    devices.Remove(deviceId);

                    if (_serialPorts.TryGetValue(portName, out var portInfo))
                    {
                        // 移除从站地址记录
                        portInfo.DeviceSlaveAddresses.Remove(deviceId);
                        portInfo.DeviceCount = devices.Count;

                        // 如果没有设备使用此端口，关闭并移除
                        if (devices.Count == 0)
                        {
                            if (portInfo.SerialPort?.IsOpen == true)
                            {
                                portInfo.SerialPort.Close();
                            }
                            portInfo.SerialPort?.Dispose();

                            _serialPorts.TryRemove(portName, out _);
                            _portDeviceMap.TryRemove(portName, out _);

                            App.AlarmService.Info("串口管理", $"端口 {portName} 已关闭并移除（无设备使用）");
                        }
                    }
                    
                    App.AlarmService.Info("串口管理", $"设备 {deviceId} 从端口 {portName} 注销");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("串口管理", $"注销设备 {deviceId} 从端口 {portName} 失败", ex);
                return false;
            }
            finally
            {
                _managementLock.Release();
            }
        }

        /// <summary>
        /// 异步获取指定端口的串口实例
        /// </summary>
        /// <param name="portName">要获取的串口名称，如 "COM1"、"COM2" 等</param>
        /// <returns>
        /// 返回串口实例：
        /// - SerialPort: 成功获取的串口实例，已打开并可用于通信
        /// - null: 获取失败（端口未注册或打开失败）
        /// </returns>
        /// <remarks>
        /// 获取过程包括以下操作：
        /// 1. 检查端口是否已注册，未注册则返回 null
        /// 2. 如果串口实例不存在，创建新的串口实例并配置参数
        /// 3. 如果串口未打开，尝试打开串口连接
        /// 4. 更新端口的最后访问时间和通信统计
        /// 5. 返回可用的串口实例
        ///
        /// 串口配置参数：
        /// - 数据位: 8
        /// - 校验位: None
        /// - 停止位: One
        /// - 读取超时: 5000ms
        /// - 写入超时: 5000ms
        ///
        /// 该方法是线程安全的，使用信号量确保并发访问的安全性。
        /// 如果串口打开失败，会记录错误日志并更新失败连接统计。
        /// </remarks>
        /// <example>
        /// <code>
        /// var manager = new SerialPortManager();
        /// var serialPort = await manager.GetSerialPortAsync("COM1");
        /// if (serialPort != null)
        /// {
        ///     // 使用串口进行通信
        ///     serialPort.Write(data, 0, data.Length);
        /// }
        /// </code>
        /// </example>
        public async Task<SerialPort?> GetSerialPortAsync(string portName)
        {
            await _managementLock.WaitAsync();
            
            try
            {
                if (!_serialPorts.TryGetValue(portName, out var portInfo))
                {
                    App.AlarmService.Warning("串口管理", $"端口 {portName} 未注册");
                    return null;
                }

                // 如果串口未创建，创建新的串口实例
                if (portInfo.SerialPort == null)
                {
                    portInfo.SerialPort = new SerialPort
                    {
                        PortName = portName,
                        BaudRate = portInfo.BaudRate,
                        DataBits = 8,
                        Parity = Parity.None,
                        StopBits = StopBits.One,
                        ReadTimeout = 5000,
                        WriteTimeout = 5000
                    };
                }

                // 如果串口未打开，尝试打开
                if (!portInfo.SerialPort.IsOpen)
                {
                    try
                    {
                        portInfo.SerialPort.Open();
                        portInfo.IsOpen = true;
                        portInfo.LastAccessTime = DateTime.Now;
                        portInfo.CommunicationQuality.ConnectionAttempts++;
                        
                        App.AlarmService.Info("串口管理", $"端口 {portName} 打开成功");
                    }
                    catch (Exception ex)
                    {
                        portInfo.CommunicationQuality.FailedConnections++;
                        App.AlarmService.Error("串口管理", $"打开端口 {portName} 失败", ex);
                        return null;
                    }
                }

                portInfo.LastAccessTime = DateTime.Now;
                return portInfo.SerialPort;
            }
            finally
            {
                _managementLock.Release();
            }
        }

        /// <summary>
        /// 记录串口通信结果，用于统计通信质量
        /// </summary>
        /// <param name="portName">串口名称</param>
        /// <param name="success">通信是否成功</param>
        /// <param name="responseTime">响应时间，仅在成功时有效</param>
        /// <remarks>
        /// 该方法用于收集通信统计数据，包括：
        /// 1. 总请求数、成功请求数、失败请求数
        /// 2. 成功率计算
        /// 3. 平均响应时间计算
        /// 4. 最后成功/失败时间记录
        /// 5. 更新端口最后访问时间
        ///
        /// 统计数据用于健康检查和通信质量评估。
        /// 如果指定的端口不存在，该方法会静默忽略。
        /// </remarks>
        /// <example>
        /// <code>
        /// var stopwatch = Stopwatch.StartNew();
        /// // 执行通信操作
        /// bool success = await CommunicateWithDevice();
        /// stopwatch.Stop();
        ///
        /// manager.RecordCommunicationResult("COM1", success, stopwatch.Elapsed);
        /// </code>
        /// </example>
        public void RecordCommunicationResult(string portName, bool success, TimeSpan responseTime)
        {
            if (_serialPorts.TryGetValue(portName, out var portInfo))
            {
                var quality = portInfo.CommunicationQuality;
                quality.TotalRequests++;

                if (success)
                {
                    quality.SuccessfulRequests++;
                    quality.TotalResponseTime += responseTime;
                    quality.LastSuccessTime = DateTime.Now;
                }
                else
                {
                    quality.FailedRequests++;
                    quality.LastFailureTime = DateTime.Now;
                }

                // 计算成功率
                quality.SuccessRate = (double)quality.SuccessfulRequests / quality.TotalRequests;

                // 计算平均响应时间
                if (quality.SuccessfulRequests > 0)
                {
                    quality.AverageResponseTime = quality.TotalResponseTime / quality.SuccessfulRequests;
                }

                portInfo.LastAccessTime = DateTime.Now;
            }
        }

        /// <summary>
        /// 获取指定端口的通信质量统计信息
        /// </summary>
        /// <param name="portName">串口名称</param>
        /// <returns>
        /// 返回通信质量对象：
        /// - CommunicationQuality: 包含详细统计信息的对象
        /// - null: 指定端口不存在
        /// </returns>
        /// <remarks>
        /// 通信质量信息包括：
        /// - 总请求数、成功请求数、失败请求数
        /// - 成功率百分比
        /// - 平均响应时间
        /// - 最后成功/失败时间
        /// - 连接尝试次数和失败连接数
        /// </remarks>
        public CommunicationQuality? GetCommunicationQuality(string portName)
        {
            return _serialPorts.TryGetValue(portName, out var portInfo) ? portInfo.CommunicationQuality : null;
        }

        /// <summary>
        /// 获取所有串口的状态信息
        /// </summary>
        /// <returns>包含所有端口状态信息的字典，键为端口名，值为状态对象</returns>
        /// <remarks>
        /// 返回的状态信息包括：
        /// - 端口基本信息（名称、波特率、是否打开）
        /// - 设备数量和最后访问时间
        /// - 通信质量统计
        /// - 设备从站地址映射（副本，避免外部修改）
        ///
        /// 该方法返回的是状态快照，不会随原始数据变化而变化。
        /// </remarks>
        public Dictionary<string, SerialPortStatus> GetAllPortStatus()
        {
            var status = new Dictionary<string, SerialPortStatus>();

            foreach (var kvp in _serialPorts)
            {
                var portInfo = kvp.Value;
                status[kvp.Key] = new SerialPortStatus
                {
                    PortName = portInfo.PortName,
                    BaudRate = portInfo.BaudRate,
                    IsOpen = portInfo.IsOpen,
                    DeviceCount = portInfo.DeviceCount,
                    LastAccessTime = portInfo.LastAccessTime,
                    Quality = portInfo.CommunicationQuality,
                    DeviceSlaveAddresses = new Dictionary<string, byte>(portInfo.DeviceSlaveAddresses)
                };
            }

            return status;
        }

        /// <summary>
        /// 获取指定端口上所有设备的从站地址映射
        /// </summary>
        /// <param name="portName">串口名称</param>
        /// <returns>
        /// 返回设备从站地址映射：
        /// - Dictionary&lt;string, byte&gt;: 设备ID到从站地址的映射字典（副本）
        /// - null: 指定端口不存在
        /// </returns>
        /// <remarks>
        /// 返回的是映射字典的副本，避免外部代码意外修改内部数据。
        /// 字典的键是设备ID，值是对应的Modbus从站地址。
        /// </remarks>
        public Dictionary<string, byte>? GetPortDeviceSlaveAddresses(string portName)
        {
            if (_serialPorts.TryGetValue(portName, out var portInfo))
            {
                return new Dictionary<string, byte>(portInfo.DeviceSlaveAddresses);
            }
            return null;
        }

        /// <summary>
        /// 检查指定从站地址在端口上是否存在冲突
        /// </summary>
        /// <param name="portName">要检查的串口名称</param>
        /// <param name="slaveAddress">要检查的从站地址</param>
        /// <param name="excludeDeviceId">要排除的设备ID，通常是当前设备自身</param>
        /// <returns>
        /// 返回冲突检查结果：
        /// - true: 存在地址冲突
        /// - false: 不存在冲突或端口不存在
        /// </returns>
        /// <remarks>
        /// 该方法用于在注册新设备或修改设备地址时检查冲突。
        /// excludeDeviceId 参数用于排除指定设备，通常用于设备自身地址更新时的检查。
        /// 如果指定的端口不存在，返回 false（无冲突）。
        /// </remarks>
        /// <example>
        /// <code>
        /// // 检查地址1是否与其他设备冲突（排除Device001自身）
        /// bool hasConflict = manager.IsSlaveAddressConflict("COM1", 1, "Device001");
        /// if (hasConflict)
        /// {
        ///     Console.WriteLine("从站地址冲突");
        /// }
        /// </code>
        /// </example>
        public bool IsSlaveAddressConflict(string portName, byte slaveAddress, string excludeDeviceId = "")
        {
            if (_serialPorts.TryGetValue(portName, out var portInfo))
            {
                foreach (var kvp in portInfo.DeviceSlaveAddresses)
                {
                    if (kvp.Value == slaveAddress && kvp.Key != excludeDeviceId)
                    {
                        return true;
                    }
                }
            }
            return false;
        }

        /// <summary>
        /// 异步强制重连指定串口
        /// </summary>
        /// <param name="portName">要重连的串口名称</param>
        /// <returns>
        /// 返回重连结果：
        /// - true: 重连成功
        /// - false: 重连失败或端口不存在
        /// </returns>
        /// <remarks>
        /// 重连过程包括以下步骤：
        /// 1. 关闭现有的串口连接
        /// 2. 释放串口资源
        /// 3. 重新创建串口实例并配置参数
        /// 4. 尝试打开新的串口连接
        /// 5. 更新连接统计和访问时间
        ///
        /// 该方法通常在检测到串口通信异常时调用，用于恢复连接。
        /// 重连操作是线程安全的，使用信号量确保并发访问的安全性。
        /// </remarks>
        /// <example>
        /// <code>
        /// // 当检测到通信异常时重连
        /// bool reconnected = await manager.ReconnectPortAsync("COM1");
        /// if (reconnected)
        /// {
        ///     Console.WriteLine("串口重连成功");
        /// }
        /// </code>
        /// </example>
        public async Task<bool> ReconnectPortAsync(string portName)
        {
            await _managementLock.WaitAsync();

            try
            {
                if (!_serialPorts.TryGetValue(portName, out var portInfo))
                {
                    return false;
                }

                App.AlarmService.Info("串口管理", $"正在重连端口 {portName}");

                // 关闭现有连接
                if (portInfo.SerialPort?.IsOpen == true)
                {
                    portInfo.SerialPort.Close();
                }
                portInfo.SerialPort?.Dispose();
                portInfo.IsOpen = false;

                // 重新创建串口实例
                portInfo.SerialPort = new SerialPort
                {
                    PortName = portName,
                    BaudRate = portInfo.BaudRate,
                    DataBits = 8,
                    Parity = Parity.None,
                    StopBits = StopBits.One,
                    ReadTimeout = 5000,
                    WriteTimeout = 5000
                };

                // 尝试打开
                try
                {
                    portInfo.SerialPort.Open();
                    portInfo.IsOpen = true;
                    portInfo.LastAccessTime = DateTime.Now;
                    portInfo.CommunicationQuality.ConnectionAttempts++;

                    App.AlarmService.Info("串口管理", $"端口 {portName} 重连成功");
                    return true;
                }
                catch (Exception ex)
                {
                    portInfo.CommunicationQuality.FailedConnections++;
                    App.AlarmService.Error("串口管理", $"端口 {portName} 重连失败", ex);
                    return false;
                }
            }
            finally
            {
                _managementLock.Release();
            }
        }

        /// <summary>
        /// 执行串口健康检查的定时任务
        /// </summary>
        /// <param name="state">定时器状态参数（未使用）</param>
        /// <remarks>
        /// 健康检查包括以下内容：
        /// 1. 检查长时间未访问的端口（超过5分钟）
        /// 2. 评估通信质量，对成功率低于80%的端口发出警告
        /// 3. 对可疑端口执行简单的可用性测试
        /// 4. 自动重连检测到问题的端口
        ///
        /// 该方法由定时器每30秒调用一次，采用异步执行避免阻塞定时器线程。
        /// 所有异常都会被捕获并记录，确保健康检查不会影响正常业务流程。
        /// </remarks>
        private async void PerformHealthCheck(object? state)
        {
            try
            {
                var now = DateTime.Now;
                var portsToCheck = new List<string>();

                // 收集需要检查的端口
                foreach (var kvp in _serialPorts)
                {
                    var portInfo = kvp.Value;

                    // 检查长时间未访问的端口
                    if ((now - portInfo.LastAccessTime).TotalMinutes > 5)
                    {
                        portsToCheck.Add(kvp.Key);
                    }

                    // 检查通信质量
                    var quality = portInfo.CommunicationQuality;
                    if (quality.TotalRequests > 10 && quality.SuccessRate < 0.8)
                    {
                        App.AlarmService.Warning("串口健康", $"端口 {kvp.Key} 通信质量较差，成功率: {quality.SuccessRate:P2}");
                    }
                }

                // 对需要检查的端口进行健康检查
                foreach (var portName in portsToCheck)
                {
                    if (_serialPorts.TryGetValue(portName, out var portInfo))
                    {
                        if (portInfo.SerialPort?.IsOpen == true)
                        {
                            try
                            {
                                // 简单的健康检查：检查串口是否仍然可用
                                var bytesToRead = portInfo.SerialPort.BytesToRead;
                                portInfo.LastAccessTime = now;
                            }
                            catch (Exception ex)
                            {
                                App.AlarmService.Warning("串口健康", $"端口 {portName} 健康检查失败，尝试重连: {ex.Message}");
                                await ReconnectPortAsync(portName);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("串口健康", "健康检查过程中发生异常", ex);
            }
        }

        #region IDisposable 实现

        /// <summary>
        /// 释放串口管理器使用的所有资源
        /// </summary>
        /// <remarks>
        /// 实现 IDisposable 接口，确保正确释放非托管资源。
        /// 调用此方法后，对象将不再可用。
        /// </remarks>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放串口管理器使用的资源
        /// </summary>
        /// <param name="disposing">
        /// 指示是否正在释放托管资源：
        /// - true: 同时释放托管和非托管资源
        /// - false: 仅释放非托管资源
        /// </param>
        /// <remarks>
        /// 释放过程包括：
        /// 1. 停止并释放健康检查定时器
        /// 2. 关闭所有打开的串口连接
        /// 3. 释放所有串口实例
        /// 4. 清空内部数据结构
        /// 5. 释放同步锁资源
        ///
        /// 该方法支持多次调用，使用 _disposed 标志防止重复释放。
        /// 所有异常都会被捕获并记录，确保释放过程的稳定性。
        /// </remarks>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                        _healthCheckTimer?.Dispose();

                        // 关闭所有串口
                        foreach (var portInfo in _serialPorts.Values)
                        {
                            if (portInfo.SerialPort?.IsOpen == true)
                            {
                                portInfo.SerialPort.Close();
                            }
                            portInfo.SerialPort?.Dispose();
                        }

                        _serialPorts.Clear();
                        _portDeviceMap.Clear();
                        _managementLock?.Dispose();

                        App.AlarmService.Info("串口管理", "串口通信管理器已释放");
                    }
                    catch (Exception ex)
                    {
                        App.AlarmService.Error("串口管理", "释放串口管理器资源时发生错误", ex);
                    }
                }

                _disposed = true;
            }
        }

        #endregion
    }

    /// <summary>
    /// 串口信息内部数据结构
    /// </summary>
    /// <remarks>
    /// 存储单个串口的完整信息，包括配置参数、连接状态、设备映射和统计数据。
    /// 该类仅供 SerialPortManager 内部使用，不对外公开。
    /// </remarks>
    internal class SerialPortInfo
    {
        /// <summary>
        /// 串口名称，如 "COM1"、"COM2" 等
        /// </summary>
        public string PortName { get; set; } = string.Empty;

        /// <summary>
        /// 波特率，同一端口上的所有设备必须使用相同的波特率
        /// </summary>
        public int BaudRate { get; set; }

        /// <summary>
        /// 串口实例，可能为 null（延迟创建）
        /// </summary>
        public SerialPort? SerialPort { get; set; }

        /// <summary>
        /// 串口是否已打开
        /// </summary>
        public bool IsOpen { get; set; }

        /// <summary>
        /// 使用此端口的设备数量
        /// </summary>
        public int DeviceCount { get; set; }

        /// <summary>
        /// 最后一次访问时间，用于健康检查
        /// </summary>
        public DateTime LastAccessTime { get; set; }

        /// <summary>
        /// 通信质量统计信息
        /// </summary>
        public CommunicationQuality CommunicationQuality { get; set; } = new();

        /// <summary>
        /// 设备ID到从站地址的映射，用于冲突检测
        /// </summary>
        public Dictionary<string, byte> DeviceSlaveAddresses { get; set; } = new();
    }

    /// <summary>
    /// 通信质量统计数据结构
    /// </summary>
    /// <remarks>
    /// 记录串口通信的各项统计指标，用于评估通信质量和系统健康状态。
    /// 所有统计数据都是累积值，从串口首次使用开始计算。
    /// </remarks>
    public class CommunicationQuality
    {
        /// <summary>
        /// 总请求数（成功 + 失败）
        /// </summary>
        public long TotalRequests { get; set; }

        /// <summary>
        /// 成功请求数
        /// </summary>
        public long SuccessfulRequests { get; set; }

        /// <summary>
        /// 失败请求数
        /// </summary>
        public long FailedRequests { get; set; }

        /// <summary>
        /// 成功率（0.0 到 1.0 之间的值）
        /// </summary>
        public double SuccessRate { get; set; }

        /// <summary>
        /// 总响应时间（所有成功请求的响应时间之和）
        /// </summary>
        public TimeSpan TotalResponseTime { get; set; }

        /// <summary>
        /// 平均响应时间（总响应时间 / 成功请求数）
        /// </summary>
        public TimeSpan AverageResponseTime { get; set; }

        /// <summary>
        /// 最后一次成功通信的时间
        /// </summary>
        public DateTime LastSuccessTime { get; set; }

        /// <summary>
        /// 最后一次失败通信的时间
        /// </summary>
        public DateTime LastFailureTime { get; set; }

        /// <summary>
        /// 连接尝试次数（包括成功和失败）
        /// </summary>
        public long ConnectionAttempts { get; set; }

        /// <summary>
        /// 失败连接次数
        /// </summary>
        public long FailedConnections { get; set; }
    }

    /// <summary>
    /// 串口状态信息数据结构
    /// </summary>
    /// <remarks>
    /// 提供串口当前状态的快照信息，用于外部查询和监控。
    /// 该类是 SerialPortInfo 的公开版本，不包含内部实现细节。
    /// </remarks>
    public class SerialPortStatus
    {
        /// <summary>
        /// 串口名称
        /// </summary>
        public string PortName { get; set; } = string.Empty;

        /// <summary>
        /// 波特率
        /// </summary>
        public int BaudRate { get; set; }

        /// <summary>
        /// 串口是否已打开
        /// </summary>
        public bool IsOpen { get; set; }

        /// <summary>
        /// 使用此端口的设备数量
        /// </summary>
        public int DeviceCount { get; set; }

        /// <summary>
        /// 最后一次访问时间
        /// </summary>
        public DateTime LastAccessTime { get; set; }

        /// <summary>
        /// 通信质量统计信息
        /// </summary>
        public CommunicationQuality Quality { get; set; } = new();

        /// <summary>
        /// 设备从站地址映射（副本，防止外部修改）
        /// </summary>
        public Dictionary<string, byte> DeviceSlaveAddresses { get; set; } = new();
    }
}
