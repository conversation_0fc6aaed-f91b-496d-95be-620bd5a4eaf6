using System;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;

namespace PEMTestSystem.Services.Devices
{
    /// <summary>
    /// Modbus RTU 设备驱动系统使用示例
    /// 展示如何使用温控器和流量泵驱动
    /// </summary>
    public class DeviceUsageExample
    {
        private readonly DeviceManager _deviceManager;
        public DeviceUsageExample(DeviceManager deviceManager)
        {
            _deviceManager = deviceManager ?? throw new ArgumentNullException(nameof(deviceManager));
        }

        /// <summary>
        /// 演示完整的设备操作流程
        /// </summary>
        public async Task RunExampleAsync()
        {
            try
            {
                App.AlarmService.Info("使用示例", "开始 Modbus RTU 设备驱动系统演示");

                // 1. 加载设备配置
                App.AlarmService.Info("使用示例", "步骤 1: 加载设备配置");
                var configLoaded = await _deviceManager.LoadDeviceConfigurationsAsync();
                if (!configLoaded)
                {
                    App.AlarmService.Error("使用示例", "设备配置加载失败");
                    return;
                }

                // 2. 初始化所有设备
                App.AlarmService.Info("使用示例", "步骤 2: 初始化设备");
                var devicesInitialized = await _deviceManager.InitializeAllDevicesAsync();
                if (!devicesInitialized)
                {
                    App.AlarmService.Error("使用示例", "设备初始化失败");
                    return;
                }

                // 3. 连接所有设备
                App.AlarmService.Info("使用示例", "步骤 3: 连接设备");
                var devicesConnected = await _deviceManager.ConnectAllDevicesAsync();
                if (!devicesConnected)
                {
                    App.AlarmService.Error("使用示例", "设备连接失败");
                    return;
                }

                // 4. 演示温控器操作
                await DemonstrateTemperatureControllerAsync();

                // 5. 演示流量泵操作
                await DemonstrateFlowPumpsAsync();

                // 6. 演示设备状态监控
                await DemonstrateDeviceMonitoringAsync();

                App.AlarmService.Info("使用示例", "Modbus RTU 设备驱动系统演示完成");
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("使用示例", "演示过程中发生异常", ex);
            }
        }

        /// <summary>
        /// 演示温控器操作
        /// </summary>
        private async Task DemonstrateTemperatureControllerAsync()
        {
            try
            {
                App.AlarmService.Info("使用示例", "=== 温控器操作演示 ===");

                var tempController = _deviceManager.GetTemperatureController("TempController_Main");
                if (tempController == null)
                {
                    App.AlarmService.Warning("使用示例", "未找到温控器设备");
                    return;
                }

                // 读取当前温度
                var currentTemp = await tempController.GetCurrentTemperatureAsync();
                App.AlarmService.Info("使用示例", $"当前温度: {currentTemp:F1}°C");

                // 读取目标温度
                var targetTemp = await tempController.GetTargetTemperatureAsync();
                App.AlarmService.Info("使用示例", $"目标温度: {targetTemp:F1}°C");

                // 设置新的目标温度
                var newTargetTemp = 30.0;
                var setResult = await tempController.SetTargetTemperatureAsync(newTargetTemp);
                if (setResult)
                {
                    App.AlarmService.Info("使用示例", $"目标温度已设置为: {newTargetTemp}°C");
                }

                // 启动加热
                var heatingStarted = await tempController.StartHeatingAsync();
                if (heatingStarted)
                {
                    App.AlarmService.Info("使用示例", "加热已启动");
                }

                // 等待一段时间
                await Task.Delay(2000);

                // 检查加热状态
                var isHeating = await tempController.GetHeatingStatusAsync();
                App.AlarmService.Info("使用示例", $"加热状态: {(isHeating ? "运行中" : "已停止")}");

                // 设置报警温度
                await tempController.SetAlarmTemperatureAsync(1, 80.0);
                App.AlarmService.Info("使用示例", "报警温度1已设置为: 80.0°C");

                // 停止加热
                await tempController.StopHeatingAsync();
                App.AlarmService.Info("使用示例", "加热已停止");
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("使用示例", "温控器操作演示异常", ex);
            }
        }

        /// <summary>
        /// 演示流量泵操作
        /// </summary>
        private async Task DemonstrateFlowPumpsAsync()
        {
            try
            {
                App.AlarmService.Info("使用示例", "=== 流量泵操作演示 ===");

                var pump1 = _deviceManager.GetFlowPump("Pump_01");
                var pump2 = _deviceManager.GetFlowPump("Pump_02");

                if (pump1 == null || pump2 == null)
                {
                    App.AlarmService.Warning("使用示例", "未找到流量泵设备");
                    return;
                }

                // 设置流量泵1
                App.AlarmService.Info("使用示例", "--- 流量泵1操作 ---");
                await pump1.SetFlowRateAsync(5.0);
                App.AlarmService.Info("使用示例", "流量泵1目标流量设置为: 5.0 L/min");

                await pump1.SetDirectionAsync(false); // 正向
                App.AlarmService.Info("使用示例", "流量泵1方向设置为: 正向");

                await pump1.StartAsync();
                App.AlarmService.Info("使用示例", "流量泵1已启动");

                // 设置流量泵2
                App.AlarmService.Info("使用示例", "--- 流量泵2操作 ---");
                await pump2.SetFlowRateAsync(3.0);
                App.AlarmService.Info("使用示例", "流量泵2目标流量设置为: 3.0 L/min");

                await pump2.SetDirectionAsync(false); // 正向
                App.AlarmService.Info("使用示例", "流量泵2方向设置为: 正向");

                await pump2.StartAsync();
                App.AlarmService.Info("使用示例", "流量泵2已启动");

                // 等待一段时间
                await Task.Delay(3000);

                // 读取实际流量
                var pump1Flow = await pump1.GetFlowRateAsync();
                var pump2Flow = await pump2.GetFlowRateAsync();
                App.AlarmService.Info("使用示例", $"流量泵1实际流量: {pump1Flow:F2} L/min");
                App.AlarmService.Info("使用示例", $"流量泵2实际流量: {pump2Flow:F2} L/min");

                // 检查运行状态
                var pump1Running = await pump1.GetRunningStatusAsync();
                var pump2Running = await pump2.GetRunningStatusAsync();
                App.AlarmService.Info("使用示例", $"流量泵1运行状态: {(pump1Running ? "运行中" : "已停止")}");
                App.AlarmService.Info("使用示例", $"流量泵2运行状态: {(pump2Running ? "运行中" : "已停止")}");

                // 停止泵
                await pump1.StopAsync();
                await pump2.StopAsync();
                App.AlarmService.Info("使用示例", "所有流量泵已停止");
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("使用示例", "流量泵操作演示异常", ex);
            }
        }

        /// <summary>
        /// 演示设备状态监控
        /// </summary>
        private async Task DemonstrateDeviceMonitoringAsync()
        {
            try
            {
                App.AlarmService.Info("使用示例", "=== 设备状态监控演示 ===");

                // 获取所有设备状态
                var deviceStatus = _deviceManager.GetAllDeviceStatus();
                App.AlarmService.Info("使用示例", $"系统中共有 {deviceStatus.Count} 个设备");

                foreach (var status in deviceStatus.Values)
                {
                    App.AlarmService.Info("使用示例", 
                        $"设备: {status.DeviceName}, 类型: {status.DeviceType}, " +
                        $"状态: {status.Status}, 连接: {(status.IsConnected ? "是" : "否")}, " +
                        $"最后通信: {status.LastCommunicationTime:HH:mm:ss}");
                }

                // 健康检查
                var healthStatus = await _deviceManager.CheckAllDevicesHealthAsync();
                var healthyCount = healthStatus.Values.Count(h => h);
                App.AlarmService.Info("使用示例", $"设备健康检查: {healthyCount}/{healthStatus.Count} 设备健康");

                // 通信质量报告
                var qualityReport = _deviceManager.GetCommunicationQualityReport();
                App.AlarmService.Info("使用示例", $"串口通信质量报告: {qualityReport.Count} 个端口");

                foreach (var port in qualityReport.Values)
                {
                    App.AlarmService.Info("使用示例", 
                        $"端口: {port.PortName}, 设备数: {port.DeviceCount}, " +
                        $"成功率: {port.Quality.SuccessRate:P2}, " +
                        $"平均响应时间: {port.Quality.AverageResponseTime.TotalMilliseconds:F1}ms");
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("使用示例", "设备状态监控演示异常", ex);
            }
        }

        /// <summary>
        /// 演示参数边界检查
        /// </summary>
        public async Task DemonstrateBoundaryCheckingAsync()
        {
            try
            {
                App.AlarmService.Info("使用示例", "=== 参数边界检查演示 ===");

                var tempController = _deviceManager.GetTemperatureController("TempController_Main");
                var pump = _deviceManager.GetFlowPump("Pump_01");

                if (tempController != null)
                {
                    // 测试温度边界
                    try
                    {
                        await tempController.SetTargetTemperatureAsync(100.0); // 超出范围
                        App.AlarmService.Warning("使用示例", "温度边界检查失败 - 应该抛出异常");
                    }
                    catch (ArgumentOutOfRangeException)
                    {
                        App.AlarmService.Info("使用示例", "✓ 温度边界检查正常 - 正确拒绝了超出范围的温度");
                    }
                }

                if (pump != null)
                {
                    // 测试流量边界
                    try
                    {
                        await pump.SetFlowRateAsync(500.0); // 超出范围
                        App.AlarmService.Warning("使用示例", "流量边界检查失败 - 应该抛出异常");
                    }
                    catch (ArgumentOutOfRangeException)
                    {
                        App.AlarmService.Info("使用示例", "✓ 流量边界检查正常 - 正确拒绝了超出范围的流量");
                    }
                }
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("使用示例", "参数边界检查演示异常", ex);
            }
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public async Task CleanupAsync()
        {
            try
            {
                App.AlarmService.Info("使用示例", "清理资源");

                // 重置所有设备
                await _deviceManager.ResetAllDevicesAsync();

                // 断开所有设备
                await _deviceManager.DisconnectAllDevicesAsync();

                App.AlarmService.Info("使用示例", "资源清理完成");
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("使用示例", "资源清理异常", ex);
            }
        }
    }
}
