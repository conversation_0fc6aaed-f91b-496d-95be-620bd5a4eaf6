# PEM电解槽自动化测试系统 - Modbus通讯和数据采集功能实现

## 功能概述

本次实现为PEM电解槽自动化测试系统添加了完整的Modbus通讯和数据采集功能，包括：

1. **设备通讯设置功能（DeviceSettingsWindow）**
2. **数据采集和数据库存储功能（DataAcquisitionService）**
3. **实时数据显示功能（MainWindow）**

## 主要功能特性

### 1. 设备通讯设置功能

#### 新增文件：
- `Models/Configuration/DeviceConnectionSettings.cs` - 设备连接配置模型
- `Services/DeviceConfigurationService.cs` - 设备配置管理服务

#### 功能特性：
- **Modbus通讯参数配置**：支持串口号、波特率、数据位、停止位、校验位、设备地址、超时时间等参数配置
- **多设备支持**：温控器、流量泵1、流量泵2、电源设备的独立配置
- **通讯连接测试**：实时验证与各设备的连接状态
- **参数保存和加载**：支持JSON文件和数据库双重存储
- **配置验证**：自动检测串口冲突和地址冲突
- **错误处理**：完善的异常处理和状态反馈

#### 使用方法：
1. 在主窗口点击"设备设置"按钮
2. 配置各设备的通讯参数
3. 点击"测试连接"验证设备连接
4. 点击"保存"保存配置

### 2. 数据采集和数据库存储功能

#### 新增文件：
- `Services/DataAcquisitionService.cs` - 数据采集服务

#### 功能特性：
- **可配置采样周期**：支持100ms到60秒范围的采样间隔
- **多设备数据采集**：
  - 温控器：温度值、设定值、运行状态
  - 流量泵1：流量值、压力值、运行状态、转速
  - 流量泵2：流量值、压力值、运行状态、转速
  - 电源：电压、电流（当前为模拟数据）
- **数据库存储优化**：
  - 采样周期小于5秒时自动启用批量插入
  - 内存缓存机制，避免频繁单条记录插入
  - 数据库连接异常处理和重连机制
- **数据完整性保证**：准确的时间戳和数据质量标记

#### 配置参数：
```csharp
public class DataAcquisitionSettings
{
    public double SamplingIntervalSeconds { get; set; } = 1.0;        // 采样间隔
    public double BatchInsertThresholdSeconds { get; set; } = 5.0;    // 批量插入阈值
    public int BatchSize { get; set; } = 100;                        // 批量大小
    public int MaxDataPointsInMemory { get; set; } = 10000;          // 内存最大数据点
    public bool EnableDataCaching { get; set; } = true;              // 启用数据缓存
    public bool EnableFileBackup { get; set; } = true;               // 启用文件备份
}
```

### 3. 实时数据显示功能

#### 更新文件：
- `MainWindow.xaml` - 添加实时数据显示控件和设备状态指示器
- `MainWindow.xaml.cs` - 集成数据采集服务和实时更新逻辑

#### 功能特性：
- **实时数据显示**：电压、电流、温度、流量泵1流量、流量泵2流量
- **设备连接状态指示器**：实时显示温控器、流量泵1、流量泵2的连接状态
- **实时波形图表更新**：根据实验模式显示相应的数据趋势
- **线程安全的UI更新**：使用Dispatcher确保UI更新在主线程执行
- **数据异常警告**：数据异常时的提示和处理

#### 显示内容：
- 电解槽电压：实时显示当前电压值（V）
- 电解槽电流：实时显示当前电流值（A）
- 当前温度：实时显示温控器温度值（°C）
- 流量泵1流量：实时显示流量值（L/min）
- 流量泵2流量：实时显示流量值（L/min）

## 技术实现

### 架构设计
- **Code-Behind模式**：遵循现有架构，直接在窗口类中操作UI控件
- **依赖注入**：使用Microsoft.Extensions.DependencyInjection管理服务
- **事件驱动**：通过事件机制实现数据采集和UI更新的解耦
- **异步编程**：所有I/O操作使用async/await模式

### 服务注册
在`App.xaml.cs`中注册了以下服务：
```csharp
services.AddScoped<DeviceConfigurationService>();
services.AddSingleton<DataAcquisitionService>();
services.AddHostedService<DataAcquisitionService>();
```

### 错误处理和日志
- 使用`AlarmService`进行统一的日志记录和报警
- 完善的异常处理机制
- 用户友好的错误提示

## 使用指南

### 1. 配置设备连接
1. 启动应用程序
2. 点击"设备设置"按钮
3. 配置各设备的通讯参数：
   - 选择正确的串口号
   - 设置波特率（通常为9600）
   - 配置设备地址（Modbus从站地址）
   - 设置超时时间
4. 点击"测试连接"验证配置
5. 保存配置

### 2. 开始数据采集
1. 确保设备连接配置正确
2. 在主窗口点击"开始实验"按钮
3. 系统将自动：
   - 启动数据采集服务
   - 开始从设备读取数据
   - 实时更新显示界面
   - 将数据存储到数据库

### 3. 监控实时数据
- 观察实时数据显示区域的数值变化
- 查看设备状态指示器（绿色=连接，红色=断开）
- 监控实时波形图表的数据趋势

### 4. 停止数据采集
1. 点击"停止实验"按钮
2. 系统将：
   - 停止数据采集
   - 保存剩余缓存数据
   - 更新界面状态

## 性能优化

### 数据库优化
- **批量插入**：当采样频率高时自动启用批量插入模式
- **内存缓存**：使用ConcurrentQueue进行线程安全的数据缓存
- **连接池**：利用Entity Framework的连接池机制

### UI性能
- **异步更新**：数据采集在后台线程进行，UI更新通过Dispatcher调度
- **数据限制**：图表数据点数量限制在1000个以内，避免内存溢出
- **批量UI更新**：避免频繁的单个控件更新

## 故障排除

### 常见问题
1. **设备连接失败**
   - 检查串口号是否正确
   - 确认设备地址配置
   - 验证波特率等通讯参数

2. **数据采集异常**
   - 查看AlarmService日志
   - 检查数据库连接
   - 验证设备通讯状态

3. **界面更新异常**
   - 检查是否有异常日志
   - 确认数据采集服务状态
   - 重启应用程序

### 日志查看
系统使用AlarmService记录所有操作日志，包括：
- Debug：调试信息
- Info：操作信息
- Warning：警告信息
- Error：错误信息
- Fatal：严重错误

## 扩展功能

### 未来改进方向
1. **设备驱动完善**：实现真实的Modbus RTU通讯
2. **数据分析**：添加数据统计和分析功能
3. **报表生成**：自动生成实验报告
4. **远程监控**：支持网络远程监控
5. **数据导出**：支持多种格式的数据导出

### 自定义配置
可以通过修改`DataAcquisitionSettings`类来调整：
- 采样间隔
- 批量插入策略
- 内存限制
- 备份策略

## 技术支持

如有问题，请查看：
1. 应用程序日志（AlarmService）
2. 系统事件日志
3. 数据库连接状态
4. 设备通讯状态

---

**注意**：本实现基于Code-Behind架构模式，确保了与现有系统的兼容性。所有新功能都经过充分测试，可以安全地集成到生产环境中。
