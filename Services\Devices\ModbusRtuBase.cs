using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace PEMTestSystem.Services.Devices
{
    /// <summary>
    /// Modbus RTU 通信基础类
    /// 提供完整的 Modbus RTU 协议栈实现
    /// </summary>
    public abstract class ModbusRtuBase : IDisposable
    {

        protected readonly SerialPort _serialPort;
        protected readonly byte _slaveAddress;
        protected readonly SemaphoreSlim _communicationLock;
        protected readonly int _timeout;
        protected readonly int _maxRetries;
        
        private bool _disposed = false;

        protected ModbusRtuBase(
            string portName,
            int baudRate,
            byte slaveAddress,
            int timeout = 5000,
            int maxRetries = 3)
        {
            _slaveAddress = slaveAddress;
            _timeout = timeout;
            _maxRetries = maxRetries;
            _communicationLock = new SemaphoreSlim(1, 1);

            _serialPort = new SerialPort
            {
                PortName = portName,
                BaudRate = baudRate,
                DataBits = 8,
                Parity = Parity.None,
                StopBits = StopBits.One,
                ReadTimeout = timeout,
                WriteTimeout = timeout
            };

            App.AlarmService.Debug("Modbus RTU", $"初始化 Modbus RTU 基类 - 端口: {portName}, 波特率: {baudRate}, 从站地址: {slaveAddress}");
        }

        /// <summary>
        /// 连接到设备
        /// </summary>
        public virtual async Task<bool> ConnectAsync()
        {
            try
            {
                if (_serialPort.IsOpen)
                {
                    App.AlarmService.Warning("Modbus RTU", "串口已经打开");
                    return true;
                }

                await Task.Run(() => _serialPort.Open());
                
                // 清空缓冲区
                _serialPort.DiscardInBuffer();
                _serialPort.DiscardOutBuffer();

                App.AlarmService.Info("设备连接", $"Modbus RTU 连接成功 - 端口: {_serialPort.PortName}, 从站地址: {_slaveAddress}");
                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备连接", $"Modbus RTU 连接失败 - 端口: {_serialPort.PortName}", ex);
                return false;
            }
        }

        /// <summary>
        /// 断开设备连接
        /// </summary>
        public virtual async Task<bool> DisconnectAsync()
        {
            try
            {
                if (_serialPort.IsOpen)
                {
                    await Task.Run(() => _serialPort.Close());
                }

                App.AlarmService.Info("设备连接", $"Modbus RTU 断开连接 - 端口: {_serialPort.PortName}, 从站地址: {_slaveAddress}");
                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备连接", $"Modbus RTU 断开连接失败 - 端口: {_serialPort.PortName}", ex);
                return false;
            }
        }

        /// <summary>
        /// 检查连接状态
        /// </summary>
        public virtual Task<bool> IsConnectedAsync()
        {
            return Task.FromResult(_serialPort?.IsOpen == true);
        }

        /// <summary>
        /// 读取保持寄存器 (功能码 03H)
        /// </summary>
        protected async Task<ushort[]?> ReadHoldingRegistersAsync(ushort startAddress, ushort quantity)
        {
            if (quantity == 0 || quantity > 125)
            {
                throw new ArgumentException("寄存器数量必须在 1-125 之间", nameof(quantity));
            }

            var request = BuildReadHoldingRegistersRequest(startAddress, quantity);
            var response = await SendRequestWithRetryAsync(request);

            if (response == null)
                return null;

            return ParseReadHoldingRegistersResponse(response, quantity);
        }

        /// <summary>
        /// 写入单个寄存器 (功能码 06H)
        /// </summary>
        protected async Task<bool> WriteSingleRegisterAsync(ushort address, ushort value)
        {
            var request = BuildWriteSingleRegisterRequest(address, value);
            var response = await SendRequestWithRetryAsync(request);

            if (response == null)
                return false;

            return ValidateWriteSingleRegisterResponse(response, address, value);
        }

        /// <summary>
        /// 读取线圈状态 (功能码 01H)
        /// </summary>
        protected async Task<bool[]?> ReadCoilsAsync(ushort startAddress, ushort quantity)
        {
            if (quantity == 0 || quantity > 2000)
            {
                throw new ArgumentException("线圈数量必须在 1-2000 之间", nameof(quantity));
            }

            var request = BuildReadCoilsRequest(startAddress, quantity);
            var response = await SendRequestWithRetryAsync(request);

            if (response == null)
                return null;

            return ParseReadCoilsResponse(response, quantity);
        }

        /// <summary>
        /// 写入单个线圈 (功能码 05H)
        /// </summary>
        protected async Task<bool> WriteSingleCoilAsync(ushort address, bool value)
        {
            var request = BuildWriteSingleCoilRequest(address, value);
            var response = await SendRequestWithRetryAsync(request);

            if (response == null)
                return false;

            return ValidateWriteSingleCoilResponse(response, address, value);
        }

        /// <summary>
        /// 带重试机制的请求发送
        /// </summary>
        private async Task<byte[]?> SendRequestWithRetryAsync(byte[] request)
        {
            await _communicationLock.WaitAsync();
            
            try
            {
                for (int attempt = 1; attempt <= _maxRetries; attempt++)
                {
                    try
                    {
                        if (!_serialPort.IsOpen)
                        {
                            App.AlarmService.Warning("设备通信", "串口未打开，尝试重新连接");
                            if (!await ConnectAsync())
                            {
                                continue;
                            }
                        }

                        var response = await SendRequestAsync(request);
                        if (response != null)
                        {
                            if (attempt > 1)
                            {
                                App.AlarmService.Info("设备通信", $"第 {attempt} 次重试成功");
                            }
                            return response;
                        }
                    }
                    catch (Exception ex)
                    {
                        App.AlarmService.Warning("设备通信", $"第 {attempt} 次通信尝试失败: {ex.Message}");
                        
                        if (attempt < _maxRetries)
                        {
                            await Task.Delay(100 * attempt); // 递增延迟
                        }
                    }
                }

                App.AlarmService.Error("设备通信", $"通信失败，已重试 {_maxRetries} 次");
                return null;
            }
            finally
            {
                _communicationLock.Release();
            }
        }

        /// <summary>
        /// 发送请求并接收响应
        /// </summary>
        private async Task<byte[]?> SendRequestAsync(byte[] request)
        {
            try
            {
                // 清空接收缓冲区
                _serialPort.DiscardInBuffer();

                // 发送请求
                await Task.Run(() => _serialPort.Write(request, 0, request.Length));
                
                App.AlarmService.Debug("Modbus RTU", $"发送请求: {BitConverter.ToString(request)}");

                // 等待响应
                await Task.Delay(20); // 帧间静默时间

                // 读取响应
                var response = await ReadResponseAsync();
                
                if (response != null)
                {
                    App.AlarmService.Debug("Modbus RTU", $"接收响应: {BitConverter.ToString(response)}");
                    
                    // 验证响应
                    if (ValidateResponse(response))
                    {
                        return response;
                    }
                    else
                    {
                        App.AlarmService.Warning("设备通信", "响应验证失败");
                    }
                }

                return null;
            }
            catch (TimeoutException)
            {
                App.AlarmService.Warning("设备通信", "通信超时");
                return null;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备通信", "通信异常", ex);
                return null;
            }
        }

        /// <summary>
        /// 读取响应数据
        /// </summary>
        private async Task<byte[]?> ReadResponseAsync()
        {
            var buffer = new List<byte>();
            var startTime = DateTime.Now;

            while ((DateTime.Now - startTime).TotalMilliseconds < _timeout)
            {
                if (_serialPort.BytesToRead > 0)
                {
                    var data = new byte[_serialPort.BytesToRead];
                    var bytesRead = await Task.Run(() => _serialPort.Read(data, 0, data.Length));
                    buffer.AddRange(data.Take(bytesRead));

                    // 检查是否接收完整
                    if (buffer.Count >= 4) // 最小响应长度
                    {
                        // 等待一小段时间确保没有更多数据
                        await Task.Delay(10);
                        if (_serialPort.BytesToRead == 0)
                        {
                            return buffer.ToArray();
                        }
                    }
                }
                else
                {
                    await Task.Delay(1);
                }
            }

            return buffer.Count > 0 ? buffer.ToArray() : null;
        }

        #region CRC 校验

        /// <summary>
        /// 计算 CRC-16 校验码
        /// </summary>
        protected static ushort CalculateCrc16(byte[] data)
        {
            ushort crc = 0xFFFF;

            for (int i = 0; i < data.Length; i++)
            {
                crc ^= data[i];

                for (int j = 0; j < 8; j++)
                {
                    if ((crc & 0x0001) != 0)
                    {
                        crc = (ushort)((crc >> 1) ^ 0xA001);
                    }
                    else
                    {
                        crc >>= 1;
                    }
                }
            }

            return crc;
        }

        /// <summary>
        /// 验证 CRC 校验码
        /// </summary>
        protected static bool ValidateCrc16(byte[] data)
        {
            if (data.Length < 4)
                return false;

            var dataWithoutCrc = new byte[data.Length - 2];
            Array.Copy(data, 0, dataWithoutCrc, 0, data.Length - 2);

            var calculatedCrc = CalculateCrc16(dataWithoutCrc);
            var receivedCrc = (ushort)(data[data.Length - 2] | (data[data.Length - 1] << 8));

            return calculatedCrc == receivedCrc;
        }

        #endregion

        #region 帧构建方法

        /// <summary>
        /// 构建读取保持寄存器请求帧
        /// </summary>
        private byte[] BuildReadHoldingRegistersRequest(ushort startAddress, ushort quantity)
        {
            var frame = new byte[8];
            frame[0] = _slaveAddress;           // 从站地址
            frame[1] = 0x03;                    // 功能码
            frame[2] = (byte)(startAddress >> 8); // 起始地址高字节
            frame[3] = (byte)(startAddress & 0xFF); // 起始地址低字节
            frame[4] = (byte)(quantity >> 8);    // 数量高字节
            frame[5] = (byte)(quantity & 0xFF);  // 数量低字节

            var crc = CalculateCrc16(frame.Take(6).ToArray());
            frame[6] = (byte)(crc & 0xFF);       // CRC 低字节
            frame[7] = (byte)(crc >> 8);         // CRC 高字节

            return frame;
        }

        /// <summary>
        /// 构建写入单个寄存器请求帧
        /// </summary>
        private byte[] BuildWriteSingleRegisterRequest(ushort address, ushort value)
        {
            var frame = new byte[8];
            frame[0] = _slaveAddress;           // 从站地址
            frame[1] = 0x06;                    // 功能码
            frame[2] = (byte)(address >> 8);    // 寄存器地址高字节
            frame[3] = (byte)(address & 0xFF);  // 寄存器地址低字节
            frame[4] = (byte)(value >> 8);      // 数据高字节
            frame[5] = (byte)(value & 0xFF);    // 数据低字节

            var crc = CalculateCrc16(frame.Take(6).ToArray());
            frame[6] = (byte)(crc & 0xFF);       // CRC 低字节
            frame[7] = (byte)(crc >> 8);         // CRC 高字节

            return frame;
        }

        /// <summary>
        /// 构建读取线圈请求帧
        /// </summary>
        private byte[] BuildReadCoilsRequest(ushort startAddress, ushort quantity)
        {
            var frame = new byte[8];
            frame[0] = _slaveAddress;           // 从站地址
            frame[1] = 0x01;                    // 功能码
            frame[2] = (byte)(startAddress >> 8); // 起始地址高字节
            frame[3] = (byte)(startAddress & 0xFF); // 起始地址低字节
            frame[4] = (byte)(quantity >> 8);    // 数量高字节
            frame[5] = (byte)(quantity & 0xFF);  // 数量低字节

            var crc = CalculateCrc16(frame.Take(6).ToArray());
            frame[6] = (byte)(crc & 0xFF);       // CRC 低字节
            frame[7] = (byte)(crc >> 8);         // CRC 高字节

            return frame;
        }

        /// <summary>
        /// 构建写入单个线圈请求帧
        /// </summary>
        private byte[] BuildWriteSingleCoilRequest(ushort address, bool value)
        {
            var frame = new byte[8];
            frame[0] = _slaveAddress;           // 从站地址
            frame[1] = 0x05;                    // 功能码
            frame[2] = (byte)(address >> 8);    // 线圈地址高字节
            frame[3] = (byte)(address & 0xFF);  // 线圈地址低字节
            frame[4] = (byte)(value ? 0xFF : 0x00); // 数据高字节
            frame[5] = 0x00;                    // 数据低字节

            var crc = CalculateCrc16(frame.Take(6).ToArray());
            frame[6] = (byte)(crc & 0xFF);       // CRC 低字节
            frame[7] = (byte)(crc >> 8);         // CRC 高字节

            return frame;
        }

        #endregion

        #region 响应解析方法

        /// <summary>
        /// 解析读取保持寄存器响应
        /// </summary>
        private ushort[]? ParseReadHoldingRegistersResponse(byte[] response, ushort expectedQuantity)
        {
            if (response.Length < 5)
                return null;

            // 检查从站地址和功能码
            if (response[0] != _slaveAddress || response[1] != 0x03)
                return null;

            var byteCount = response[2];
            var expectedByteCount = expectedQuantity * 2;

            if (byteCount != expectedByteCount || response.Length != byteCount + 5)
                return null;

            var registers = new ushort[expectedQuantity];
            for (int i = 0; i < expectedQuantity; i++)
            {
                var highByte = response[3 + i * 2];
                var lowByte = response[4 + i * 2];
                registers[i] = (ushort)((highByte << 8) | lowByte);
            }

            return registers;
        }

        /// <summary>
        /// 验证写入单个寄存器响应
        /// </summary>
        private bool ValidateWriteSingleRegisterResponse(byte[] response, ushort address, ushort value)
        {
            if (response.Length != 8)
                return false;

            // 检查从站地址和功能码
            if (response[0] != _slaveAddress || response[1] != 0x06)
                return false;

            // 检查地址和数据是否匹配
            var responseAddress = (ushort)((response[2] << 8) | response[3]);
            var responseValue = (ushort)((response[4] << 8) | response[5]);

            return responseAddress == address && responseValue == value;
        }

        /// <summary>
        /// 解析读取线圈响应
        /// </summary>
        private bool[]? ParseReadCoilsResponse(byte[] response, ushort expectedQuantity)
        {
            if (response.Length < 4)
                return null;

            // 检查从站地址和功能码
            if (response[0] != _slaveAddress || response[1] != 0x01)
                return null;

            var byteCount = response[2];
            var expectedByteCount = (expectedQuantity + 7) / 8; // 向上取整

            if (byteCount != expectedByteCount || response.Length != byteCount + 5)
                return null;

            var coils = new bool[expectedQuantity];
            for (int i = 0; i < expectedQuantity; i++)
            {
                var byteIndex = i / 8;
                var bitIndex = i % 8;
                var dataByte = response[3 + byteIndex];
                coils[i] = (dataByte & (1 << bitIndex)) != 0;
            }

            return coils;
        }

        /// <summary>
        /// 验证写入单个线圈响应
        /// </summary>
        private bool ValidateWriteSingleCoilResponse(byte[] response, ushort address, bool value)
        {
            if (response.Length != 8)
                return false;

            // 检查从站地址和功能码
            if (response[0] != _slaveAddress || response[1] != 0x05)
                return false;

            // 检查地址和数据是否匹配
            var responseAddress = (ushort)((response[2] << 8) | response[3]);
            var responseValue = response[4] == 0xFF;

            return responseAddress == address && responseValue == value;
        }

        /// <summary>
        /// 验证响应帧
        /// </summary>
        private bool ValidateResponse(byte[] response)
        {
            if (response.Length < 4)
            {
                App.AlarmService.Warning("设备通信", "响应帧长度不足");
                return false;
            }

            // 检查从站地址
            if (response[0] != _slaveAddress)
            {
                App.AlarmService.Warning("设备通信", $"从站地址不匹配，期望: {_slaveAddress}, 实际: {response[0]}");
                return false;
            }

            // 检查是否为错误响应
            if ((response[1] & 0x80) != 0)
            {
                var functionCode = (byte)(response[1] & 0x7F);
                var exceptionCode = response[2];
                App.AlarmService.Error("设备通信", $"设备返回异常 - 功能码: 0x{functionCode:X2}, 异常码: 0x{exceptionCode:X2}");
                return false;
            }

            // 验证 CRC
            if (!ValidateCrc16(response))
            {
                App.AlarmService.Warning("设备通信", "CRC 校验失败");
                return false;
            }

            return true;
        }

        #endregion

        #region IDisposable 实现

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                        _serialPort?.Close();
                        _serialPort?.Dispose();
                        _communicationLock?.Dispose();
                    }
                    catch (Exception ex)
                    {
                        App.AlarmService.Error("Modbus RTU", "释放 Modbus RTU 资源时发生错误", ex);
                    }
                }

                _disposed = true;
            }
        }

        #endregion
    }
}
