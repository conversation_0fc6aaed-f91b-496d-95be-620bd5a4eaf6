---
type: "always_apply"
---

# .NET Development Rules (Code-Behind 模式)

您是一位资深的 .NET 后端开发者，专精于 C# WPF 项目、.NET 8 和 Entity Framework Core。

## Code-Behind 架构规范

### 1. 架构模式
- 使用 Code-Behind 编程模型，而非 MVVM 框架
- 在窗口类中直接操作 UI 控件
- 通过事件处理器响应用户交互
- 使用依赖注入进行服务管理

### 2. 项目结构
按照 .NET 约定组织文件结构：
```
├── Data/              # 数据访问层
├── Models/            # 数据模型
├── Services/          # 业务服务
├── Windows/           # 窗口文件
├── UserControls/      # 用户控件
├── Converters/        # 值转换器
└── Resources/         # 资源文件
```

## 代码风格和结构

### 1. C# 编码规范
- 编写简洁、符合习惯的 C# 代码
- 遵循 .NET Core 约定和最佳实践
- 适当使用面向对象和函数式编程模式
- 优先使用 LINQ 和 lambda 表达式进行集合操作
- 使用描述性的变量和方法名（如 `IsUserSignedIn`、`CalculateTotal`）

### 2. 命名约定
- 类名、方法名和公共成员使用 PascalCase
- 局部变量和私有字段使用 camelCase
- 常量使用 UPPERCASE
- 接口名以 "I" 开头（如 `IUserService`）
- 事件处理器使用 `On[Event]` 或 `[Control]_[Event]` 格式

### 3. C# 和 .NET 使用
- 适当使用 C# 10+ 特性（如 record 类型、模式匹配、null 合并赋值）
- 有效使用 Entity Framework Core 进行数据库操作
- 使用 `var` 进行隐式类型推断（当类型明显时）

## Code-Behind 窗口开发规范

### 1. 窗口类结构
```csharp
public partial class MainWindow : Window
{
    // 私有字段
    private readonly ILogger<MainWindow> _logger;
    private readonly IDataService _dataService;
    private readonly Timer _updateTimer;
    
    // 构造函数
    public MainWindow(ILogger<MainWindow> logger, IDataService dataService)
    {
        InitializeComponent();
        _logger = logger;
        _dataService = dataService;
        
        InitializeServices();
        SetupEventHandlers();
    }
    
    // 初始化方法
    private void InitializeServices() { }
    private void SetupEventHandlers() { }
    
    // 事件处理器
    private void StartButton_Click(object sender, RoutedEventArgs e) { }
    private void OnDataReceived(object sender, DataEventArgs e) { }
    
    // UI 更新方法
    private void UpdateDeviceStatus(DeviceStatus status) { }
    private void UpdateDataDisplay(ExperimentData data) { }
    
    // 资源清理
    protected override void OnClosed(EventArgs e)
    {
        _updateTimer?.Dispose();
        base.OnClosed(e);
    }
}
```

### 2. 服务注入模式
```csharp
// 在 App.xaml.cs 中配置服务
public partial class App : Application
{
    public static IHost Host { get; private set; }
    public static AlarmService AlarmService { get; private set; }
    
    protected override void OnStartup(StartupEventArgs e)
    {
        Host = CreateHostBuilder().Build();
        AlarmService = Host.Services.GetRequiredService<AlarmService>();
        
        var mainWindow = Host.Services.GetRequiredService<MainWindow>();
        mainWindow.Show();
    }
}

// 在窗口中使用服务
public MainWindow(IDataService dataService, IDeviceService deviceService)
{
    InitializeComponent();
    _dataService = dataService;
    _deviceService = deviceService;
}
```

### 3. 直接控件操作
```csharp
// 推荐：直接更新 UI 控件
private void UpdateDeviceStatus(DeviceStatus status)
{
    StatusLabel.Content = status.IsOnline ? "在线" : "离线";
    StatusIndicator.Fill = status.IsOnline ? Brushes.Green : Brushes.Red;
    ConnectionButton.Content = status.IsOnline ? "断开" : "连接";
    ConnectionButton.IsEnabled = true;
}

private void UpdateExperimentData(ExperimentData data)
{
    VoltageTextBox.Text = data.Voltage.ToString("F2");
    CurrentTextBox.Text = data.Current.ToString("F2");
    TemperatureLabel.Content = $"{data.Temperature:F1}°C";
    
    // 更新图表
    UpdateChart(data);
}
```

### 4. 事件驱动更新
```csharp
private void SetupEventHandlers()
{
    // 订阅服务事件
    _dataService.DataReceived += OnDataReceived;
    _deviceService.StatusChanged += OnDeviceStatusChanged;
    _experimentService.StateChanged += OnExperimentStateChanged;
}

private void OnDataReceived(object sender, DataEventArgs e)
{
    // 确保在 UI 线程中更新
    Dispatcher.Invoke(() =>
    {
        UpdateDataDisplay(e.Data);
        UpdateChart(e.Data);
    });
}

private void OnDeviceStatusChanged(object sender, DeviceStatusEventArgs e)
{
    Dispatcher.Invoke(() =>
    {
        UpdateDeviceStatusDisplay(e.Status);
        App.AlarmService.Info("设备状态", $"设备 {e.DeviceId} 状态变更为 {e.Status}");
    });
}
```

### 5. 线程安全的 UI 更新
```csharp
// 后台线程安全更新 UI
private void UpdateUIFromBackgroundThread(ExperimentData data)
{
    if (Dispatcher.CheckAccess())
    {
        // 已在 UI 线程中
        UpdateDataDisplay(data);
    }
    else
    {
        // 需要调度到 UI 线程
        Dispatcher.Invoke(() => UpdateDataDisplay(data));
    }
}

// 异步更新模式
private async void StartExperiment_Click(object sender, RoutedEventArgs e)
{
    try
    {
        StartButton.IsEnabled = false;
        App.AlarmService.Info("实验控制", "开始启动实验");
        
        await _experimentService.StartExperimentAsync();
        
        ExperimentStatusLabel.Content = "实验运行中";
        StopButton.IsEnabled = true;
    }
    catch (Exception ex)
    {
        App.AlarmService.Error("实验控制", "启动实验失败", ex);
        MessageBox.Show($"启动实验失败：{ex.Message}", "错误", 
                       MessageBoxButton.OK, MessageBoxImage.Error);
    }
    finally
    {
        StartButton.IsEnabled = true;
    }
}
```

## 性能优化

### 1. 异步编程
- 对所有 I/O 绑定工作使用异步编程（async/await）
- 包括文件访问、Modbus 通信、TCP/UDP 网络操作
- 保持线程不被阻塞，提高可扩展性

```csharp
private async void ConnectDevice_Click(object sender, RoutedEventArgs e)
{
    try
    {
        ConnectButton.IsEnabled = false;
        StatusLabel.Content = "连接中...";
        
        await _deviceService.ConnectAsync();
        
        StatusLabel.Content = "已连接";
        App.AlarmService.Info("设备连接", "设备连接成功");
    }
    catch (Exception ex)
    {
        StatusLabel.Content = "连接失败";
        App.AlarmService.Error("设备连接", "设备连接失败", ex);
    }
    finally
    {
        ConnectButton.IsEnabled = true;
    }
}
```

### 2. 缓存策略
- 使用 IMemoryCache 或分布式缓存实现缓存策略
- 使用高效的 LINQ 查询，避免 N+1 查询问题
- 对大数据集实现分页

### 3. UI 性能优化
```csharp
// 批量更新 UI 以提高性能
private readonly List<DataPoint> _pendingUpdates = new();
private readonly Timer _uiUpdateTimer;

private void OnDataReceived(object sender, DataEventArgs e)
{
    lock (_pendingUpdates)
    {
        _pendingUpdates.Add(e.Data);
    }
}

private void UpdateUI_Timer_Tick(object sender, EventArgs e)
{
    List<DataPoint> updates;
    lock (_pendingUpdates)
    {
        updates = new List<DataPoint>(_pendingUpdates);
        _pendingUpdates.Clear();
    }
    
    if (updates.Count > 0)
    {
        Dispatcher.Invoke(() => ProcessBatchUpdates(updates));
    }
}
```

## 错误处理和验证

### 1. 异常处理
- 异常用于异常情况，不用于控制流
- 实现全局异常处理
- 返回适当的状态码和一致的错误响应

```csharp
// 全局异常处理
private void Application_DispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
{
    App.AlarmService.Fatal("系统错误", "未处理的异常", e.Exception);
    
    MessageBox.Show($"系统发生严重错误：{e.Exception.Message}\n\n应用程序将关闭。", 
                   "严重错误", MessageBoxButton.OK, MessageBoxImage.Error);
    
    e.Handled = true;
    Application.Current.Shutdown();
}

// 方法级异常处理
private async void SaveData_Click(object sender, RoutedEventArgs e)
{
    try
    {
        await _dataService.SaveExperimentDataAsync(GetCurrentData());
        App.AlarmService.Info("数据保存", "实验数据保存成功");
        StatusLabel.Content = "数据已保存";
    }
    catch (ValidationException ex)
    {
        App.AlarmService.Warning("数据验证", ex.Message);
        MessageBox.Show(ex.Message, "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
    }
    catch (Exception ex)
    {
        App.AlarmService.Error("数据保存", "保存数据时发生错误", ex);
        MessageBox.Show($"保存失败：{ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
    }
}
```

### 2. 输入验证
```csharp
private bool ValidateInput()
{
    if (string.IsNullOrWhiteSpace(VoltageTextBox.Text))
    {
        MessageBox.Show("请输入电压值", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
        VoltageTextBox.Focus();
        return false;
    }
    
    if (!decimal.TryParse(VoltageTextBox.Text, out decimal voltage) || voltage < 0 || voltage > 10)
    {
        MessageBox.Show("电压值必须在 0-10V 范围内", "验证错误", MessageBoxButton.OK, MessageBoxImage.Warning);
        VoltageTextBox.SelectAll();
        VoltageTextBox.Focus();
        return false;
    }
    
    return true;
}
```

## 日志和报警规范（使用 AlarmService）

### 1. AlarmService 使用模式
- 使用 `App.AlarmService` 进行所有日志记录和报警通知
- 遵循一致的日志记录模式
- 正确分类报警级别

### 2. 标准日志记录模式

#### Debug: 开发调试信息
```csharp
App.AlarmService.Debug("模块名", "调试信息");
App.AlarmService.Debug("数据采集", $"接收到数据点：{dataPoint}");
```

#### Info: 操作和状态信息
```csharp
App.AlarmService.Info("设备控制", "已连接到设备");
App.AlarmService.Info("实验管理", $"实验 {experimentId} 已开始");
```

#### Warning: 需要注意但不影响运行的问题
```csharp
App.AlarmService.Warning("参数验证", "参数接近限制范围");
App.AlarmService.Warning("设备通信", "通信延迟较高");
```

#### Error: 运行错误但可恢复
```csharp
App.AlarmService.Error("设备通信", "通信超时", exception);
App.AlarmService.Error("数据处理", "数据格式错误", exception);
```

#### Fatal: 严重错误需要立即处理
```csharp
App.AlarmService.Fatal("系统错误", "关键组件初始化失败", exception);
App.AlarmService.Fatal("安全系统", "紧急停止触发", exception);
```

### 3. 消息指南
- 消息应简洁明了
- 包含必要的上下文信息
- 错误信息应包含原因和建议
- 使用一致的信息格式

### 4. 最佳实践
1. 合理使用日志级别
2. 提供清晰的上下文信息
3. 避免过度日志记录
4. 确保异常信息完整性
5. 保持消息的一致性格式

## 关键约定

### 1. 依赖注入
- 使用依赖注入实现松耦合和可测试性
- 在 `App.xaml.cs` 中配置服务容器
- 通过构造函数注入服务到窗口类

### 2. 数据访问
- 根据复杂性直接使用 Entity Framework Core 或实现仓储模式
- 需要时使用 AutoMapper 进行对象映射
- 使用 IHostedService 或 BackgroundService 实现后台任务

### 3. 配置管理
```csharp
// 在服务中使用配置
public class DataAcquisitionService
{
    private readonly IConfiguration _configuration;
    
    public DataAcquisitionService(IConfiguration configuration)
    {
        _configuration = configuration;
    }
    
    private double GetMaxVoltage()
    {
        return _configuration.GetValue<double>("MaxVoltage", 10.0);
    }
}
```

## 测试

### 1. 单元测试
- 使用 xUnit、NUnit 或 MSTest 编写单元测试
- 使用 Moq 或 NSubstitute 模拟依赖项
- 为 API 端点实现集成测试

### 2. UI 测试考虑
```csharp
// 将业务逻辑从 UI 中分离以便测试
public class ExperimentController
{
    public async Task<bool> StartExperimentAsync(ExperimentParameters parameters)
    {
        // 业务逻辑
        return true;
    }
}

// 在窗口中使用
private async void StartButton_Click(object sender, RoutedEventArgs e)
{
    var parameters = GetExperimentParameters();
    var success = await _experimentController.StartExperimentAsync(parameters);
    
    if (success)
    {
        UpdateUIForRunningExperiment();
    }
}
```

## 语法和格式

### 1. C# 编码约定
- 遵循 C# 编码约定 (https://docs.microsoft.com/en-us/dotnet/csharp/fundamentals/coding-style/coding-conventions)
- 使用 C# 的表达性语法（如 null 条件运算符、字符串插值）

### 2. 代码示例
```csharp
// 使用现代 C# 特性
public record ExperimentData(decimal Voltage, decimal Current, DateTime Timestamp);

// 模式匹配
private string GetStatusMessage(DeviceStatus status) => status switch
{
    DeviceStatus.Online => "设备在线",
    DeviceStatus.Offline => "设备离线",
    DeviceStatus.Error => "设备错误",
    _ => "未知状态"
};

// Null 条件运算符
private void UpdateDevice()
{
    _device?.UpdateStatus();
    StatusLabel.Content = _device?.Status?.ToString() ?? "未知";
}
```

## 注意事项

### 1. 避免的模式
- ❌ 不使用 INotifyPropertyChanged 接口
- ❌ 不使用 BindableBase 基类  
- ❌ 不使用 GlobalDataManager 单例
- ❌ 避免复杂的 XAML 数据绑定（除简单静态绑定外）
- ❌ 不使用属性通知机制

### 2. 推荐的模式
- ✅ 直接控件操作
- ✅ 事件处理器
- ✅ 服务注入到窗口类
- ✅ 线程安全的 UI 更新（Dispatcher.Invoke）
- ✅ 清晰的错误处理和用户反馈

### 3. 性能考虑
- 注意线程安全
- 实现适当的错误处理
- 添加必要的日志记录
- 考虑性能优化
- 避免 UI 线程阻塞