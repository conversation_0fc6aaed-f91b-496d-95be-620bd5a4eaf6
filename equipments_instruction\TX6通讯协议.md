
### 二 TX6 通讯协议资料

适用范围: TX6 系列产品
标准规约: 用 Modbus 规约的 RTU 模式

### 三 概述内容:

#### 1. 字元结构 (10-bit 子元框) 和波特率

资料格式 8.N.1

| START | 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | STOP |
| :--- | :-- | :-- | :-- | :-- | :-- | :-- | :-- | :-- | :-- |
| 1 Bit | | | | | | | | | 1 Bit |
| |<--|---|---|---|---|---|---|---|-->|
| | 8-data bits | | | | | | | | |
| |<--|---|---|---|---|---|---|---|---|
| | 10-bits character fram | | | | | | | | |

校验方式: 无奇偶校验位, CRC16 循环校验, 一 个启动位, 一个停止位
波特率: 2.4K, 4.8K, 9.6K, 14.4K, 19.2K, 28.8K, 38.4K bps
数据: 十六进制, 二进制补码

#### 2.通信资料结构

##### 2.1 资料格式框

| | |
| :--- | :--- |
| START | 保持无输入讯号 〉= 20ms |
| Adress | 通信位址:8-bit 二进制位址 |
| Function | 功能码: 8-bit 二进制位址 |
| DATA(n-1) | 资料内容: |
| ..... | N*8-bit 资料, n<=13 |
| DATA 0 | |
| CRC CHK Low | CRC 检查码: |
| CRC CHK High | 16 bit CRC 检查码由 2 个 8-bit 二进制组合 |
| END | 保持无输入讯号 〉= 20ms |

##### 2.2 通信位置 (Adress)

00H: 所有控制器广播 (Broadcast)
01H: 对第 01 位址控制器
0FH: 对第 15 位址控制器
10H: 对第 16 位址控制器
以此类推 ......, 最大可到 254 (FEH)。

##### 2.3 功能码 (Function) 与资料内容 (Data Characters)

03H: 读出控制器暫存器内容
06H: 写入控制器暫存器内容
3 功能码 03H: 读出暂存器内容。

例如: 对控制器位址 01H, 读出两个连续于暂存器内的资料内容如下表示; 起始暂存器位址为 0000H

**询问讯息格式:**

| | |
| :--- | :--- |
| 仪表地址 | 01H |
| 功能码 | 03H |
| 读取起始地址 | 00H |
| 00h |
| 读取数据个数 | 00H |
| 两字节 | 03H |
| CRC16 校验码低字节 | 05H |
| CRC16 校验码高字节 | CBH |

**回应讯息格式**

| | |
| :--- | :--- |
| 仪表地址 | 01H |
| 功能码 | 03H |
| 连续返回字节数 | 06H |
| 一个字节 |
| 参数地址 0000h | 00H |
| 数值 (两字节) | 64H |
| 参数地址 0001h | 00H |
| 数值 (两字节) | 96H |
| 参数地址 0002h | 00H |
| 数值 (两字节) | 05H |
| CRC16 校验码低字节 | E0H |
| CRC16 校验码高字节 | 92H |

说明: 读出的测量值 PV 为 170, 控温设定值 SV 为 200, 输出 1 指示灯和报警 1 指示灯亮。
4 功能码 06H: 写入一个 WORD 至暂存器。

例如: 对控制器位址 01H, 写入 200 (00C8H) 至控制器暂存器位址 0001H(SV 设定值)。

**询问讯息格式:**

| | |
| :--- | :--- |
| Address | 01H |
| Function | 06H |
| Data address | 00H |
| 01H |
| Data content | 00H |
| C8H |
| CRC CHK Low | D9H |
| CRC CHK Hight | 9CH |

**回应讯息格式:**

| | |
| :--- | :--- |
| Address | 01H |
| Function | 06H |
| Data address | 00H |
| 01H |
| Data content | 00H |
| C8H |
| CRC CHK Low | D9H |
| CRC CHK Hight | 9CH |

#### 6 RTU 模式的检查码 (CRC Check)

检查码由 Address 到 Data content 结束。其运算规则如下:
步骤 1: 令 16-bit 暂存器 (CRC 暂存器) = FFFFH。
步骤 2: Exclusive OR 第一个 8-bite byte 的讯息指令与低位元 16-bite CRC 暂存器, 做 Exclusive OR, 将结果存入 CRC 暂存器内。
步骤 3: 右移位 CRC 暂存器, 将 0 填入高位元处。
步骤 4: 检查右移的值, 如果是 0, 将步骤 3 的新值存入 CRC 暂存器内, 否则 Exclusive OR A001H 与 CRC 暂存器, 将结果存入 CRC 暂存器内。
步骤 5: 重复步骤 3~步骤 4, 将 8-bit 全部运算完成。
步骤 6: 重复步骤 2~步骤 5, 取下一个 8-bit 的讯息指令, 直到所有讯息指令运算完成。最后, 得到的 CRC 暂存器的值, 即是 CRC 的检查码。值得注意的是 CRC 的检查码必须交换放置於讯息指令的检查码中。
以下为用 c 语言所写的 crc 检查码运算范例:

```c
unsigned char *data;
unsigned char length;
unsigned int crc_chk(unsigned char *data, unsigned char length)
{
    int j;
    unsigned int reg_crc = 0xffff;
    while (length--) {
        reg_crc ^= *data++;
        for (j = 0; j < 8; j++) {
            if (reg_crc & 0x01) {
                reg_crc = (reg_crc >> 1) ^ 0xa001;
            } else {
                reg_crc = reg_crc >> 1;
            }
        }
    }
    return reg_crc;
}
```

### 四 控制器的参数地址定义:

| 地址 | 代号 | 说明 | 参数范围 | 出厂值 | 读/写 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| 0000H---40001 | PV | 测量值 注 1 | | | R |
| 0001H---40002 | SV | 目标温度控制值 | 全量程 | 150 | R/W |
| 0002H---40003 | LED | LED 显示仪表各种工作状态 注 2 | | | R |
| 0003H---40004 | OUTB | 当前输出百分比 | 0.0---100.0% | 0 | R |
| 0004H---40005 | AT/ONOFF | 自整定和关闭打开加热开关 | | 000CH=10+2 | R/W |
| | | 写 01 启动自整定, 写 02 关闭自整定 | | | |
| | | 写 0A 打开加热, 写 14 关闭加热 | | | |
| | | 关闭加热时下排显示 STOP | | | |
| | | 写其它值无效 | | | |
| 0005H---40006 | AL1 | 报警值 1 | 全量程 | | R/W |
| 0006H---40007 | AL2 | 报警值 2 | 全量程 | | R/W |
| 0007H---40008 | AH1 | 报警回差 1 | 全量程 | | R/W |
| 0008H---40009 | AH2 | 报警回差 2 | 全量程 | | R/W |
| 0009H---40010 | P | 比例项 | 1---9999 | | R/W |
| 000AH---40011 | I | 积分项 | 00---9999 | | R/W |
| 000BH---40012 | D | 微分项 | 00---9999 | | R/W |
| 000CH---40013 | AR | 积分过冲抑制百分比 | 1---100 | | R/W |
| 000DH---40014 | T | 控制周期 | 1---100 秒 | | R/W |
| 000EH---40015 | EC | 测量值偏差修正 | -1000---+1000 | 0 | R/W |
| 000FH---40016 | EK | 测量值斜率修正 | -1000---+1000 | 0 | R/W |
| 0010H---40017 | OH | 主控回差,位式控制时 | 全量程 | | R/W |
| 0011H---40018 | OF | 主控回差,位式控制时 | 全量程 | | R/W |
| 0012H---40019 | AD1 | 报警方式 1 | 0---100 | | R/W |
| 0013H---40020 | AD2 | 报警方式 2 | 0---100 | | R/W |
| 0014H---40021 | SL-L | 传感器量程下限 | | 0 | R/W |
| 0015H---40022 | SL-H | 传感器量程上限 | | 1300 | R/W |
| 0016H---40023 | SUL | 限制控制值 SV 下限 | | 0 | R/W |
| 0017H---40024 | SUH | 限制控制值 SV 上限 | | 1300 | R/W |
| 0018H---40025 | PL | 限制最低加热输出百分比, | 0.0---30.0% | 0.0 | R/W |
| 0019H---40026 | PH | 限制最高加热输出百分比, | 3.0---100.0% | 100.0 | R/W |
| 001AH---40027 | PC | 输出 2 比例项 | 0---1000 | | R/W |
| 001BH---40028 | TC | 输出 2 控制周期 | 1---100 秒 | | R/W |
| 001CH---40029 | DB | 不感带 | -1000---+1000 | | R/W |

注 1: 测量值用二进制补码表示, -16666(BEE6H)时表示下溢出, 仪表显示 LLLL
18888 (49C8H) 时表示上溢出, 仪表显示 HHHH

注 2: 低字节对应面板上指示灯为:
BIT0 OUT1(主控输出 1) =1 表示当前状态有 OUT1 输出-----0001
BIT1 OUT2(输出 2) =1 表示当前状态有 OUT2 输出----0002
BIT2 AT(自整定) =1 表示当前在自整定状态-----0004
BIT3 AL1(报警 1) =1 表示当前报警 1 有输出-----0008
BIT4 AL2(报警 2) =1 表示当前报警 2 有输出---0016
BIT5 ON/OFF =1(0032) 下排显示 STOP,表示当前关闭加热, =0 当前在加热运行
BIT6 =1(0064) 测量单位为华氏度, =0 摄氏度
BIT7 =1 加热环路 LBAT 报警
BIT8 BIT9 为小数点, =00 无小数点, =01 10 11 分别为 1—3 个小数点
BIT10—BIT15 未定义

### 六 错误通信时的额外回应:

当控制器做通信连接时, 如果产生错误, 此时控制器会回应错误码且将 Function code AND 80H 回应给主控系统, 让主控系统知道有错误产生。
参考错误通信时错误码的意义。
RTU 模式:

| | |
| :--- | :--- |
| Address | 01H |
| Function | 86H |
| Exception code | 02H |
| CRC CHK Low | 09H |
| CRC CHK Hight | D5H |

错误码的意义:

| 错误码 | 说明 |
| :--- | :--- |
| 01 | 功能码错误; |
| | 控制器可以辨识的功能码为 03H,06H,08H |
| 02 | 资料位址错误; |
| | 资料的位址控制器无法辨识 |
| 03 | 资料内容值错误 |
| | 资料内容值太大或者太小, 不是控制器所能辨识的内容值 |
| 04 | 控制器无法处理; |
| | 控制器对此命令, 无法执行 |
| 06 | 控制器忙线中; |
| | 控制器正在处理资料中, 请将指令字串间隔放宽 |
| 09 | 检查码错误 |
| | 指令子串中的检查码是错误的。 |
| 11 | Frame error:字元 frame 错误 |
| 12 | 指令字串中的讯息字节太短 |
| 13 | 指令字串中的讯息字节太长。 |