卡川尔流体科技(上海)有限公司
BIP/CIP/DIP 485通讯协议
***
# BIP/CIP/DIP 485 通讯协议

## 一、RS485 协议参数设置
1. 485通讯协议: Modbus协议
2. 串口设置: 9600-8-N-1 (波特率 9600, 数据位8位, 无校验位, 停止位1位)
3. 通讯地址: 选项菜单c011设置地址
4. 通讯波特率: 选项菜单c012
   0: 1200
   1: 2400
   2: 4800
   3: 9600
   4: 19200
   5: 38400
   6: 57600
   7: 115200

## 二、RS485协议内容
| 功能 | 地址 | 操作 |
| --- | --- | --- |
| 线圈 | 0x1001 | 读写 |
| 离散量 | 0x2001 | 只读 |
| 保持寄存器 | 0x3001 | 读写 |
| 输入寄存器 | 0x4001 | 只读 |

07/23
***
卡川尔流体科技(上海)有限公司
BIP/CIP/DIP 485通讯协议
***
### 线圈
| 地址 | 功能 | 值 |
| --- | --- | --- |
| 0x1001 | 起停 | 停止(0)/启动(1) |
| 0x1002 | 排空（默认最大转速运行） | 关闭(0)/开始(1) |
| 0x1003 | 方向 | 正(0)/反(1) |
| 0x1004 | 485 控制使能位 | 使能(1)/禁止(0) |
| 0x1005 | 模拟量校准使能位 | 使能(1)/禁止(0) |
| 0x1006 | 模拟量单点数据保存 | 保存(1) |
| 0x1007 | 模拟量校准完成 | 校准完成(1) |
| 0x1008 | 模拟量类型 | 0~5V(0)/4~20mA(1) |
| 0x1009 | 重新上电记录运行状态 | 0: 不记录 1: 记录 |
| 0x100a | 保存参数到 Flash | 写 1 保存转速 |

### 保存寄存器
| 地址 | 功能 | 描述 |
| --- | --- | --- |
| 0x3001 | 转速（单精度浮点数） | 高 16 位 |
| 0x3002 | 转速（单精度浮点数） | 低 16 位 |
| 0x3003 | 模拟量值（单精度浮点数） | 高 16 位 |
| 0x3004 | 模拟量低（单精度浮点数） | 低 16 位 |
| 0x3005 | 实时转速（单精度浮点数） | 高 16 位 |
| 0x3006 | 实时转速（单精度浮点数） | 低 16 位 |
| 0x3007 | 反转时长（整数，单位秒） | 自动模式回吸时长, 0代表不回吸 |
| 0x3008 | 脚踏开关模式 | 0: 触发运行<br>1: 高电平运行<br>2: 低电平运行 |
| 0x3009 | 转速（整型*100）RPM | 高16位 |
| 0x300a | 低16位 |
| 0x300b | 实时转速（整型*100）RPM | 高16位 |
| 0x300c | 低16位 |

**注意：不同版本对应的最大转速不一致，转速输入范围不能大于最大转速。**
以上 0x1009 上电记录运行状态，设置的是 485 和旋转编码器工作模式。当记录运行状态打开时，设备断电重启保持之前的运行状态，比如之前是运行的，重新上电继续运行。

12/20
***
卡川尔流体科技(上海)有限公司
BIP/CIP/DIP 485 通讯协议
***
### 调试软件命令
| 命令 | 命令 |
| --- | --- |
| 关闭电机(10) | 打开电机(11) |
| 关闭排空(20) | 打开排空(21) |
| 方向(30) | 方向(31) |
| 使能 485 控制(41) | 禁止 485 控制(40) |
| 模拟量校准关闭(50) | 模拟量校准开启(51) |
| 模拟量存入清零(60) | 模拟存入(61) |
| 校准完成清零(70) | 校准完成使能(71) |
| 模拟量选择 0~5V(80) | 模拟量选择 4~20mA(81) |
| 设置速度(888) | |
| 设置模拟量实际值(999) | |

## 三、RS485上电后使用步骤
1. 首先需要设置 485 使能为 1。
2. 如果只是想 485 控制，就可以直接发送相关控制指令，如果需要用来重新校准模拟量需要打开模拟量校准使能位。
3. 模拟量校准步骤首先是接入真实模拟量并将真实模拟量值发送给 485 的模拟对应寄存器，然后选择触发保存，这样一个点的数据就存入校准寄存器，如此重复采样3个以上的点数之后即可触发校准完成，这样即可完成模拟量校准，最后退出模拟量校准即可正常使用 485 控制功能。

