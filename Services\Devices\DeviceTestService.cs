using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace PEMTestSystem.Services.Devices
{
    /// <summary>
    /// 设备测试服务
    /// 用于验证设备通信和功能
    /// </summary>
    public class DeviceTestService
    {
        private readonly DeviceManager _deviceManager;
        private readonly ILogger<DeviceTestService> _logger;

        public DeviceTestService(DeviceManager deviceManager, ILogger<DeviceTestService> logger)
        {
            _deviceManager = deviceManager ?? throw new ArgumentNullException(nameof(deviceManager));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// 测试所有设备连接
        /// </summary>
        public async Task<TestResult> TestAllDeviceConnectionsAsync()
        {
            var result = new TestResult { TestName = "设备连接测试" };
            
            try
            {
                App.AlarmService.Info("设备测试", "开始测试所有设备连接");

                var devices = _deviceManager.GetAllDevices();
                var totalDevices = 0;
                var connectedDevices = 0;

                foreach (var device in devices)
                {
                    totalDevices++;
                    
                    try
                    {
                        var connected = await device.IsConnectedAsync();
                        if (connected)
                        {
                            connectedDevices++;
                            result.Details.Add($"✓ {device.DeviceName} 连接正常");
                        }
                        else
                        {
                            result.Details.Add($"✗ {device.DeviceName} 连接失败");
                        }
                    }
                    catch (Exception ex)
                    {
                        result.Details.Add($"✗ {device.DeviceName} 测试异常: {ex.Message}");
                    }
                }

                result.Success = connectedDevices > 0;
                result.Summary = $"连接测试完成: {connectedDevices}/{totalDevices} 设备连接正常";
                
                App.AlarmService.Info("设备测试", result.Summary);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Summary = $"连接测试异常: {ex.Message}";
                App.AlarmService.Error("设备测试", "设备连接测试异常", ex);
            }

            return result;
        }

        /// <summary>
        /// 测试温控器功能
        /// </summary>
        public async Task<TestResult> TestTemperatureControllerAsync(string deviceId)
        {
            var result = new TestResult { TestName = "温控器功能测试" };
            
            try
            {
                var tempController = _deviceManager.GetTemperatureController(deviceId);
                if (tempController == null)
                {
                    result.Success = false;
                    result.Summary = $"未找到温控器设备: {deviceId}";
                    return result;
                }

                App.AlarmService.Info("设备测试", $"开始测试温控器 {tempController.DeviceName}");

                // 测试连接
                var connected = await tempController.IsConnectedAsync();
                if (!connected)
                {
                    result.Success = false;
                    result.Summary = "温控器未连接";
                    return result;
                }
                result.Details.Add("✓ 温控器连接正常");

                // 测试读取当前温度
                try
                {
                    var currentTemp = await tempController.GetCurrentTemperatureAsync();
                    result.Details.Add($"✓ 当前温度读取成功: {currentTemp:F1}°C");
                }
                catch (Exception ex)
                {
                    result.Details.Add($"✗ 当前温度读取失败: {ex.Message}");
                }

                // 测试读取目标温度
                try
                {
                    var targetTemp = await tempController.GetTargetTemperatureAsync();
                    result.Details.Add($"✓ 目标温度读取成功: {targetTemp:F1}°C");
                }
                catch (Exception ex)
                {
                    result.Details.Add($"✗ 目标温度读取失败: {ex.Message}");
                }

                // 测试设置目标温度（安全范围内）
                try
                {
                    var testTemp = 25.0;
                    var setResult = await tempController.SetTargetTemperatureAsync(testTemp);
                    if (setResult)
                    {
                        result.Details.Add($"✓ 目标温度设置成功: {testTemp}°C");
                        
                        // 验证设置是否生效
                        await Task.Delay(1000);
                        var verifyTemp = await tempController.GetTargetTemperatureAsync();
                        if (Math.Abs(verifyTemp - testTemp) < 0.5)
                        {
                            result.Details.Add("✓ 目标温度设置验证成功");
                        }
                        else
                        {
                            result.Details.Add($"✗ 目标温度设置验证失败: 期望 {testTemp}°C, 实际 {verifyTemp:F1}°C");
                        }
                    }
                    else
                    {
                        result.Details.Add("✗ 目标温度设置失败");
                    }
                }
                catch (Exception ex)
                {
                    result.Details.Add($"✗ 目标温度设置异常: {ex.Message}");
                }

                // 测试参数边界检查
                try
                {
                    await tempController.SetTargetTemperatureAsync(100.0); // 超出范围
                    result.Details.Add("✗ 参数边界检查失败（应该抛出异常）");
                }
                catch (ArgumentOutOfRangeException)
                {
                    result.Details.Add("✓ 参数边界检查正常");
                }
                catch (Exception ex)
                {
                    result.Details.Add($"✗ 参数边界检查异常: {ex.Message}");
                }

                result.Success = true;
                result.Summary = $"温控器 {tempController.DeviceName} 功能测试完成";
                
                App.AlarmService.Info("设备测试", result.Summary);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Summary = $"温控器测试异常: {ex.Message}";
                App.AlarmService.Error("设备测试", "温控器功能测试异常", ex);
            }

            return result;
        }

        /// <summary>
        /// 测试流量泵功能
        /// </summary>
        public async Task<TestResult> TestFlowPumpAsync(string deviceId)
        {
            var result = new TestResult { TestName = "流量泵功能测试" };
            
            try
            {
                var flowPump = _deviceManager.GetFlowPump(deviceId);
                if (flowPump == null)
                {
                    result.Success = false;
                    result.Summary = $"未找到流量泵设备: {deviceId}";
                    return result;
                }

                App.AlarmService.Info("设备测试", $"开始测试流量泵 {flowPump.DeviceName}");

                // 测试连接
                var connected = await flowPump.IsConnectedAsync();
                if (!connected)
                {
                    result.Success = false;
                    result.Summary = "流量泵未连接";
                    return result;
                }
                result.Details.Add("✓ 流量泵连接正常");

                // 确保泵处于停止状态
                await flowPump.StopAsync();
                result.Details.Add("✓ 流量泵停止");

                // 测试读取当前流量
                try
                {
                    var currentFlow = await flowPump.GetFlowRateAsync();
                    result.Details.Add($"✓ 当前流量读取成功: {currentFlow:F2} L/min");
                }
                catch (Exception ex)
                {
                    result.Details.Add($"✗ 当前流量读取失败: {ex.Message}");
                }

                // 测试设置流量（安全范围内）
                try
                {
                    var testFlow = 1.0;
                    var setResult = await flowPump.SetFlowRateAsync(testFlow);
                    if (setResult)
                    {
                        result.Details.Add($"✓ 目标流量设置成功: {testFlow} L/min");
                    }
                    else
                    {
                        result.Details.Add("✗ 目标流量设置失败");
                    }
                }
                catch (Exception ex)
                {
                    result.Details.Add($"✗ 目标流量设置异常: {ex.Message}");
                }

                // 测试方向设置
                try
                {
                    var dirResult = await flowPump.SetDirectionAsync(false);
                    if (dirResult)
                    {
                        result.Details.Add("✓ 泵方向设置成功（正向）");
                    }
                    else
                    {
                        result.Details.Add("✗ 泵方向设置失败");
                    }
                }
                catch (Exception ex)
                {
                    result.Details.Add($"✗ 泵方向设置异常: {ex.Message}");
                }

                // 测试参数边界检查
                try
                {
                    await flowPump.SetFlowRateAsync(500.0); // 超出范围
                    result.Details.Add("✗ 参数边界检查失败（应该抛出异常）");
                }
                catch (ArgumentOutOfRangeException)
                {
                    result.Details.Add("✓ 参数边界检查正常");
                }
                catch (Exception ex)
                {
                    result.Details.Add($"✗ 参数边界检查异常: {ex.Message}");
                }

                result.Success = true;
                result.Summary = $"流量泵 {flowPump.DeviceName} 功能测试完成";
                
                App.AlarmService.Info("设备测试", result.Summary);
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.Summary = $"流量泵测试异常: {ex.Message}";
                App.AlarmService.Error("设备测试", "流量泵功能测试异常", ex);
            }

            return result;
        }

        /// <summary>
        /// 运行完整的设备测试套件
        /// </summary>
        public async Task<TestSuiteResult> RunFullTestSuiteAsync()
        {
            var suiteResult = new TestSuiteResult { SuiteName = "Modbus RTU 设备测试套件" };
            
            try
            {
                App.AlarmService.Info("设备测试", "开始运行完整设备测试套件");

                // 1. 连接测试
                var connectionTest = await TestAllDeviceConnectionsAsync();
                suiteResult.TestResults.Add(connectionTest);

                // 2. 温控器测试
                var tempController = _deviceManager.GetDevicesByType<ITemperatureController>().FirstOrDefault();
                if (tempController != null)
                {
                    var tempTest = await TestTemperatureControllerAsync(tempController.DeviceId);
                    suiteResult.TestResults.Add(tempTest);
                }

                // 3. 流量泵测试
                var flowPumps = _deviceManager.GetDevicesByType<IFlowPump>().ToList();
                foreach (var pump in flowPumps)
                {
                    var pumpTest = await TestFlowPumpAsync(pump.DeviceId);
                    suiteResult.TestResults.Add(pumpTest);
                }

                var successCount = suiteResult.TestResults.Count(r => r.Success);
                suiteResult.Success = successCount > 0;
                suiteResult.Summary = $"测试套件完成: {successCount}/{suiteResult.TestResults.Count} 项测试通过";

                App.AlarmService.Info("设备测试", suiteResult.Summary);
            }
            catch (Exception ex)
            {
                suiteResult.Success = false;
                suiteResult.Summary = $"测试套件异常: {ex.Message}";
                App.AlarmService.Error("设备测试", "设备测试套件异常", ex);
            }

            return suiteResult;
        }
    }

    /// <summary>
    /// 测试结果
    /// </summary>
    public class TestResult
    {
        public string TestName { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string Summary { get; set; } = string.Empty;
        public List<string> Details { get; set; } = new();
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 测试套件结果
    /// </summary>
    public class TestSuiteResult
    {
        public string SuiteName { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string Summary { get; set; } = string.Empty;
        public List<TestResult> TestResults { get; set; } = new();
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }
}
