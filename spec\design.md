# 设计文档

## 概述

PEM电解槽实验测试系统采用单进程架构设计，集成数据采集服务和用户界面功能。系统使用C# WPF Code-Behind模式开发，SQL Server作为数据存储，支持三种实验模式的高精度数据采集和长期实验的稳定运行。通过多线程设计确保UI响应性和数据采集的连续性。

## 架构

### 系统架构图

```mermaid
graph TB
    subgraph "主应用程序 (单进程)"
        subgraph "UI线程"
            UI[WPF用户界面]
            UILogic[界面逻辑层]
        end
        
        subgraph "后台线程"
            DataService[数据采集服务]
            DeviceManager[设备管理器]
            DataProcessor[数据处理器]
            ExperimentController[实验控制器]
        end
    end
    
    subgraph "设备层"
        PowerSupply[可编程直流电源<br/>爱德克斯IT-M3901D]
        Pump1[流量泵1<br/>卡川DI Pump550]
        Pump2[流量泵2<br/>卡川DI Pump550]
        TempController[温控器<br/>宇电MK008]
    end
    
    subgraph "数据存储"
        SQLServer[(SQL Server数据库)]
        FileStorage[文件存储<br/>实时数据备份]
    end
    
    UI --> UILogic
    UILogic <--> DataService : 线程间通讯
    DataService --> DeviceManager
    DataService --> DataProcessor
    DataService --> ExperimentController
    
    DeviceManager --> PowerSupply
    DeviceManager --> Pump1
    DeviceManager --> Pump2
    DeviceManager --> TempController
    
    DataProcessor --> SQLServer
    DataProcessor --> FileStorage
    
    ExperimentController --> DeviceManager
```

### 多线程架构

系统采用多线程设计，UI线程负责用户交互，后台线程负责数据采集和设备控制：

```mermaid
sequenceDiagram
    participant UI as UI线程
    participant DS as 数据采集线程
    participant DB as SQL Server
    participant DEV as 设备
    
    UI->>DS: 发送实验参数 (事件/委托)
    DS->>DEV: 配置设备参数
    DS->>DB: 保存实验配置
    DS->>UI: 确认配置完成 (Dispatcher.Invoke)
    
    UI->>DS: 开始实验命令
    DS->>DEV: 启动温控器和水泵
    DS->>DS: 等待设备就绪
    DS->>DEV: 启动电源
    DS->>UI: 实验开始通知 (Dispatcher.Invoke)
    
    loop 数据采集循环
        DS->>DEV: 读取设备数据
        DS->>DB: 保存数据到数据库
        DS->>FileStorage: 备份数据到文件
        DS->>UI: 发送实时数据 (Dispatcher.BeginInvoke)
    end
```

## 组件和接口

### 1. 后台服务组件

#### 1.1 数据采集服务 (DataAcquisitionService)

**职责**：
- 在后台线程中运行，提供持续的数据采集能力
- 管理实验生命周期
- 通过事件和委托与UI线程通讯

**主要接口**：
```csharp
public interface IDataAcquisitionService
{
    Task<bool> StartExperimentAsync(ExperimentConfig config);
    Task<bool> StopExperimentAsync();
    Task<bool> PauseExperimentAsync();
    Task<ExperimentStatus> GetExperimentStatusAsync();
    Task<List<DataPoint>> GetRealtimeDataAsync(int count);
    Task<bool> UpdateDeviceParametersAsync(DeviceParameters parameters);
    
    // 线程安全的事件通知
    event EventHandler<DataPointEventArgs> DataPointReceived;
    event EventHandler<ExperimentStatusEventArgs> StatusChanged;
    event EventHandler<DeviceStatusEventArgs> DeviceStatusChanged;
}

// 线程间通讯的实现
public class DataAcquisitionService : IDataAcquisitionService
{
    private readonly Dispatcher _uiDispatcher;
    private readonly CancellationTokenSource _cancellationTokenSource;
    private Task _dataAcquisitionTask;
    
    public DataAcquisitionService(Dispatcher uiDispatcher)
    {
        _uiDispatcher = uiDispatcher;
        _cancellationTokenSource = new CancellationTokenSource();
    }
    
    // 向UI线程发送数据的方法
    private void NotifyDataPoint(DataPoint dataPoint)
    {
        _uiDispatcher.BeginInvoke(() => 
        {
            DataPointReceived?.Invoke(this, new DataPointEventArgs(dataPoint));
        });
    }
}
```

#### 1.2 设备管理器 (DeviceManager)

**职责**：
- 管理所有硬件设备的连接和通讯
- 提供统一的设备控制接口
- 监控设备状态和健康度

**设备接口定义**：
```csharp
public interface IDevice
{
    string DeviceId { get; }
    DeviceType Type { get; }
    DeviceStatus Status { get; }
    Task<bool> ConnectAsync();
    Task<bool> DisconnectAsync();
    Task<bool> IsConnectedAsync();
}

public interface IPowerSupply : IDevice
{
    Task<bool> SetVoltageAsync(double voltage);
    Task<bool> SetCurrentAsync(double current);
    Task<double> GetVoltageAsync();
    Task<double> GetCurrentAsync();
    Task<bool> SetOutputAsync(bool enabled);
}

public interface IFlowPump : IDevice
{
    Task<bool> SetFlowRateAsync(double flowRate);
    Task<double> GetFlowRateAsync();
    Task<bool> StartAsync();
    Task<bool> StopAsync();
}

public interface ITemperatureController : IDevice
{
    Task<bool> SetTargetTemperatureAsync(double temperature);
    Task<double> GetCurrentTemperatureAsync();
    Task<double> GetTargetTemperatureAsync();
    Task<bool> StartHeatingAsync();
    Task<bool> StopHeatingAsync();
}
```

#### 1.3 实验控制器 (ExperimentController)

**职责**：
- 实现三种实验模式的控制逻辑
- 管理实验状态机
- 处理实验前置条件检查

**实验模式接口**：
```csharp
public interface IExperimentMode
{
    ExperimentType Type { get; }
    Task<bool> InitializeAsync(ExperimentConfig config);
    Task<bool> ExecuteStepAsync();
    Task<bool> IsCompleteAsync();
    Task CleanupAsync();
}

public class ConstantCurrentMode : IExperimentMode
{
    // 恒定电流模式实现
}

public class ConstantVoltageMode : IExperimentMode
{
    // 恒定电压模式实现
}

public class LinearVoltageRampMode : IExperimentMode
{
    // 线性提升电压模式实现
}
```

### 2. 用户界面组件

#### 2.1 主窗口 (MainWindow)

**职责**：
- 提供主要的用户交互界面
- 显示实时数据和图表
- 管理实验参数设置

**主要功能区域**：
- 实验模式选择区
- 参数设置区（动态显示）
- 参数模板管理区
- 实时数据显示区
- 图表显示区
- 设备状态监控区
- 控制按钮区

#### 2.2 实验参数管理界面

**参数配置面板 (ExperimentParameterPanel)**：
```csharp
public class ExperimentParameterPanel : UserControl
{
    public ExperimentType CurrentExperimentType { get; set; }
    public ExperimentConfig CurrentConfig { get; set; }
    
    public event EventHandler<ExperimentTypeChangedEventArgs> ExperimentTypeChanged;
    public event EventHandler<ParameterChangedEventArgs> ParameterChanged;
    
    // 动态显示参数控件
    public void ShowParametersForExperimentType(ExperimentType type)
    {
        // 清除现有控件
        ParameterStackPanel.Children.Clear();
        
        // 添加通用参数控件
        AddCommonParameterControls();
        
        // 根据实验类型添加特定参数控件
        switch (type)
        {
            case ExperimentType.ConstantCurrent:
                AddConstantCurrentParameterControls();
                break;
            case ExperimentType.ConstantVoltage:
                AddConstantVoltageParameterControls();
                break;
            case ExperimentType.LinearVoltageRamp:
                AddLinearVoltageRampParameterControls();
                break;
        }
    }
    
    // 参数验证
    public ValidationResult ValidateParameters()
    {
        var validator = new ParameterValidator();
        return validator.ValidateExperimentConfig(CurrentConfig);
    }
    
    // 参数模板管理
    public void LoadTemplate(ExperimentTemplate template)
    {
        CurrentConfig = template.Configuration;
        UpdateUIFromConfig();
    }
    
    public async Task SaveAsTemplateAsync(string templateName, string description)
    {
        var template = new ExperimentTemplate
        {
            Name = templateName,
            Description = description,
            ExperimentType = CurrentExperimentType,
            Configuration = CurrentConfig
        };
        
        await _templateService.SaveTemplateAsync(template);
    }
    
    public async Task<List<ExperimentTemplate>> GetAvailableTemplatesAsync()
    {
        return await _templateService.GetTemplatesAsync(CurrentExperimentType);
    }
    
    // 实时参数调整支持（需求12和需求18）
    public async Task<bool> UpdateRuntimeParameterAsync(string parameterName, double newValue)
    {
        // 验证参数范围
        var validationResult = ValidateParameterValue(parameterName, newValue);
        if (!validationResult.IsValid)
        {
            ShowParameterError(validationResult.ErrorMessage);
            return false;
        }
        
        // 发送到运行时参数控制器
        var success = await _runtimeParameterController.UpdateParameterAsync(parameterName, newValue);
        
        if (success)
        {
            // 更新界面显示
            UpdateParameterDisplay(parameterName, newValue);
        }
        
        return success;
    }
}

// 参数模板管理服务
public interface IExperimentTemplateService
{
    Task<List<ExperimentTemplate>> GetTemplatesAsync(ExperimentType? type = null);
    Task<ExperimentTemplate> GetTemplateAsync(Guid templateId);
    Task<ExperimentTemplate> SaveTemplateAsync(ExperimentTemplate template);
    Task<bool> DeleteTemplateAsync(Guid templateId);
    Task<ExperimentTemplate> GetDefaultTemplateAsync(ExperimentType type);
    Task<bool> SetDefaultTemplateAsync(Guid templateId);
}
```

**参数模板选择对话框 (TemplateSelectionDialog)**：
```csharp
public partial class TemplateSelectionDialog : Window
{
    public ExperimentTemplate SelectedTemplate { get; private set; }
    
    private async void LoadTemplates()
    {
        var templates = await _templateService.GetTemplatesAsync();
        TemplateListBox.ItemsSource = templates;
    }
    
    private void LoadButton_Click(object sender, RoutedEventArgs e)
    {
        SelectedTemplate = TemplateListBox.SelectedItem as ExperimentTemplate;
        DialogResult = true;
    }
    
    private async void DeleteButton_Click(object sender, RoutedEventArgs e)
    {
        if (TemplateListBox.SelectedItem is ExperimentTemplate template)
        {
            var result = MessageBox.Show($"确定要删除模板 '{template.Name}' 吗？", 
                "确认删除", MessageBoxButton.YesNo, MessageBoxImage.Question);
            
            if (result == MessageBoxResult.Yes)
            {
                await _templateService.DeleteTemplateAsync(template.Id);
                await LoadTemplates();
            }
        }
    }
}
```

#### 2.2 数据可视化组件

**实时图表组件**：
- 使用OxyPlot库实现高性能图表显示
- 支持实时数据更新
- 提供缩放、平移等交互功能

```csharp
public class RealtimeChartControl : UserControl
{
    public PlotModel PlotModel { get; set; }
    public void AddDataPoint(double x, double y);
    public void ClearData();
    public void SetAxisLabels(string xLabel, string yLabel);
}
```

#### 2.3 实验参数管理界面

**参数配置面板**：
```csharp
public class ExperimentParameterPanel : UserControl
{
    public ExperimentType CurrentExperimentType { get; set; }
    public ExperimentConfig CurrentConfig { get; set; }
    
    public event EventHandler<ExperimentTypeChangedEventArgs> ExperimentTypeChanged;
    public event EventHandler<ParameterChangedEventArgs> ParameterChanged;
    
    // 动态显示参数控件
    public void ShowParametersForExperimentType(ExperimentType type);
    
    // 参数验证
    public ValidationResult ValidateParameters();
    
    // 参数模板管理
    public void LoadTemplate(ExperimentTemplate template);
    public void SaveAsTemplate(string templateName, string description);
    public List<ExperimentTemplate> GetAvailableTemplates();
}

// 参数模板管理服务
public interface IExperimentTemplateService
{
    Task<List<ExperimentTemplate>> GetTemplatesAsync(ExperimentType? type = null);
    Task<ExperimentTemplate> GetTemplateAsync(Guid templateId);
    Task<ExperimentTemplate> SaveTemplateAsync(ExperimentTemplate template);
    Task<bool> DeleteTemplateAsync(Guid templateId);
    Task<ExperimentTemplate> GetDefaultTemplateAsync(ExperimentType type);
    Task<bool> SetDefaultTemplateAsync(Guid templateId);
}
```

#### 2.4 设备控制面板

**设备参数控制**：
```csharp
public class DeviceControlPanel : UserControl
{
    public event EventHandler<DeviceParameterChangedEventArgs> ParameterChanged;
    public void UpdateDeviceStatus(DeviceStatus status);
    public void SetParameterValue(string parameter, double value);
}
```

## 数据模型

### 数据库设计

#### 实验表 (Experiments)
```sql
CREATE TABLE Experiments (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(255) NOT NULL,
    ExperimentType INT NOT NULL, -- 1:恒定电流, 2:恒定电压, 3:线性提升电压
    StartTime DATETIME2 NOT NULL,
    EndTime DATETIME2 NULL,
    Status INT NOT NULL, -- 1:准备中, 2:运行中, 3:已完成, 4:已停止, 5:错误
    Configuration NVARCHAR(MAX) NOT NULL, -- JSON格式的实验配置
    CreatedAt DATETIME2 DEFAULT GETDATE(),
    UpdatedAt DATETIME2 DEFAULT GETDATE()
);
```

#### 数据点表 (DataPoints)
```sql
CREATE TABLE DataPoints (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    ExperimentId UNIQUEIDENTIFIER NOT NULL,
    Timestamp DATETIME2 NOT NULL,
    ElapsedSeconds DECIMAL(18,3) NOT NULL,
    Voltage DECIMAL(10,3) NOT NULL,
    Current DECIMAL(10,2) NOT NULL,
    Temperature DECIMAL(8,2) NULL,
    FlowRate1 DECIMAL(8,2) NULL,
    FlowRate2 DECIMAL(8,2) NULL,
    CreatedAt DATETIME2 DEFAULT GETDATE(),
    FOREIGN KEY (ExperimentId) REFERENCES Experiments(Id)
);

-- 创建索引以提高查询性能
CREATE INDEX IX_DataPoints_ExperimentId_Timestamp 
ON DataPoints(ExperimentId, Timestamp);
```

#### 设备状态表 (DeviceStatus)
```sql
CREATE TABLE DeviceStatus (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    ExperimentId UNIQUEIDENTIFIER NULL,
    DeviceType INT NOT NULL, -- 1:电源, 2:水泵1, 3:水泵2, 4:温控器
    DeviceId NVARCHAR(50) NOT NULL,
    Status INT NOT NULL, -- 1:离线, 2:在线, 3:运行中, 4:错误
    Parameters NVARCHAR(MAX) NULL, -- JSON格式的设备参数
    Timestamp DATETIME2 NOT NULL,
    CreatedAt DATETIME2 DEFAULT GETDATE(),
    FOREIGN KEY (ExperimentId) REFERENCES Experiments(Id)
);
```

#### 实验参数模板表 (ExperimentTemplates)
```sql
CREATE TABLE ExperimentTemplates (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(255) NOT NULL,
    Description NVARCHAR(1000) NULL,
    ExperimentType INT NOT NULL, -- 1:恒定电流, 2:恒定电压, 3:线性提升电压
    Configuration NVARCHAR(MAX) NOT NULL, -- JSON格式的参数配置
    IsDefault BIT DEFAULT 0, -- 是否为默认模板
    CreatedAt DATETIME2 DEFAULT GETDATE(),
    UpdatedAt DATETIME2 DEFAULT GETDATE()
);

-- 为每种实验类型创建默认模板的索引
CREATE INDEX IX_ExperimentTemplates_Type_Default 
ON ExperimentTemplates(ExperimentType, IsDefault);
```

#### 系统日志表 (SystemLogs)
```sql
CREATE TABLE SystemLogs (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    Level INT NOT NULL, -- 1:Debug, 2:Info, 3:Warning, 4:Error, 5:Fatal
    Message NVARCHAR(MAX) NOT NULL,
    Exception NVARCHAR(MAX) NULL,
    Source NVARCHAR(255) NULL,
    ExperimentId UNIQUEIDENTIFIER NULL,
    Timestamp DATETIME2 NOT NULL,
    CreatedAt DATETIME2 DEFAULT GETDATE(),
    FOREIGN KEY (ExperimentId) REFERENCES Experiments(Id)
);
```

### C# 数据模型

```csharp
public class ExperimentConfig
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    public ExperimentType Type { get; set; }
    public int CycleCount { get; set; }
    
    // 所有模式通用参数
    public CommonParameters Common { get; set; }
    
    // 模式特定参数
    public ConstantCurrentParameters ConstantCurrent { get; set; }
    public ConstantVoltageParameters ConstantVoltage { get; set; }
    public LinearVoltageRampParameters LinearVoltageRamp { get; set; }
    
    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
}

// 所有实验模式的通用参数
public class CommonParameters
{
    public double TargetTemperature { get; set; } // 目标温度 (°C)
    public double FlowRate1 { get; set; } // 流量泵1流量 (L/min)
    public double FlowRate2 { get; set; } // 流量泵2流量 (L/min)
    public int RepeatCount { get; set; } // 重复次数
}

// 恒定电流模式参数
public class ConstantCurrentParameters
{
    public double TargetCurrent { get; set; } // 目标电流 (A)
    public double Duration { get; set; } // 实验持续时间 (秒)
    public double VoltageUpperLimit { get; set; } // 电压上限保护 (V)
    public double VoltageLowerLimit { get; set; } // 电压下限保护 (V)
    public double SamplingInterval { get; set; } // 采样间隔 (秒)
}

// 恒定电压模式参数
public class ConstantVoltageParameters
{
    public double TargetVoltage { get; set; } // 目标电压 (V)
    public double Duration { get; set; } // 实验持续时间 (秒)
    public double CurrentUpperLimit { get; set; } // 电流上限保护 (A)
    public double CurrentLowerLimit { get; set; } // 电流下限保护 (A)
    public double SamplingInterval { get; set; } // 采样间隔 (秒)
}

// 线性提升电压模式参数
public class LinearVoltageRampParameters
{
    public double StartVoltage { get; set; } // 起始电压 (V)
    public double EndVoltage { get; set; } // 终点电压 (V)
    public double RampTime { get; set; } // 变化时间 (秒) - 与RampRate二选一
    public double RampRate { get; set; } // 变化斜率 (V/s) - 与RampTime二选一
    public bool UseRampTime { get; set; } // true: 使用变化时间, false: 使用变化斜率
    public double CurrentUpperLimit { get; set; } // 电流上限保护 (A)
    public double CurrentLowerLimit { get; set; } // 电流下限保护 (A)
    public double SamplingInterval { get; set; } // 采样间隔 (秒)
    public double HoldTimeAtEnd { get; set; } // 到达终点电压后的保持时间 (秒)
}

public class DataPoint
{
    public long Id { get; set; }
    public Guid ExperimentId { get; set; }
    public DateTime Timestamp { get; set; }
    public double ElapsedSeconds { get; set; }
    public double Voltage { get; set; }
    public double Current { get; set; }
    public double? Temperature { get; set; }
    public double? FlowRate1 { get; set; }
    public double? FlowRate2 { get; set; }
}

public class ExperimentTemplate
{
    public Guid Id { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public ExperimentType ExperimentType { get; set; }
    public ExperimentConfig Configuration { get; set; }
    public bool IsDefault { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class DeviceParameters
{
    public double TargetTemperature { get; set; }
    public double TargetFlowRate1 { get; set; }
    public double TargetFlowRate2 { get; set; }
    public double VoltageLimit { get; set; }
    public double CurrentLimit { get; set; }
}
```

### 实验参数详细分析

#### 参数分类表

| 参数类别                 | 恒定电流模式 | 恒定电压模式 | 线性提升电压模式 | 说明                          |
| ------------------------ | ------------ | ------------ | ---------------- | ----------------------------- |
| **通用参数**             |             |             |                 | 所有模式都需要                |
| 目标温度 (°C)            | ✓            | ✓            | ✓                | 温控器设定值，范围：室温~90°C |
| 流量泵1流量 (L/min)      | ✓            | ✓            | ✓                | 范围：0.1~400 L/min          |
| 流量泵2流量 (L/min)      | ✓            | ✓            | ✓                | 范围：0.1~400 L/min          |
| 重复次数                 | ✓            | ✓            | ✓                | 循环实验次数，范围：1~999     |
| 采样间隔 (秒)            | ✓            | ✓            | ✓                | 数据采集频率，范围：0.1~60秒  |
| **恒定电流模式特有**      |             |           |                 |                               |
| 目标电流 (A)             | ✓            | ✗            | ✗                | 范围：0~170A，精度：0.01A     |
| 实验持续时间 (秒)        | ✓            | ✗            | ✗                | 范围：1~999999秒              |
| 电压上限保护 (V)         | ✓            | ✗            | ✗                | 范围：0~10V，精度：0.001V     |
| 电压下限保护 (V)         | ✓            | ✗            | ✗                | 范围：0~10V，精度：0.001V     |
| **恒定电压模式特有**     |             |             |                 |                               |
| 目标电压 (V)             | ✗            | ✓            | ✗                | 范围：0~10V，精度：0.001V     |
| 实验持续时间 (秒)        | ✗            | ✓            | ✗                | 范围：1~999999秒              |
| 电流上限保护 (A)         | ✗            | ✓            | ✗                | 范围：0~170A，精度：0.01A     |
| 电流下限保护 (A)         | ✗            | ✓            | ✗                | 范围：0~170A，精度：0.01A     |
| **线性提升电压模式特有**   |             |             |                |                               |
| 起始电压 (V)             | ✗            | ✗            | ✓                | 范围：0~10V，精度：0.001V     |
| 终点电压 (V)             | ✗            | ✗            | ✓                | 范围：0~10V，精度：0.001V     |
| 变化时间 (秒)            | ✗            | ✗            | ✓                | 与变化斜率二选一              |
| 变化斜率 (V/s)           | ✗            | ✗            | ✓                | 与变化时间二选一              |
| 电流上限保护 (A)         | ✗            | ✗            | ✓                | 范围：0~170A，精度：0.01A     |
| 电流下限保护 (A)         | ✗            | ✗            | ✓                | 范围：0~170A，精度：0.01A     |
| 终点保持时间 (秒)        | ✗            | ✗            | ✓                | 到达终点电压后的保持时间      |



#### 参数验证规则

```csharp
public class ParameterValidator
{
    public ValidationResult ValidateCommonParameters(CommonParameters common)
    {
        var result = new ValidationResult();
        
        // 温度验证
        if (common.TargetTemperature < 20 || common.TargetTemperature > 90)
            result.AddError("目标温度必须在20°C到90°C之间");
            
        // 流量验证
        if (common.FlowRate1 < 0.1 || common.FlowRate1 > 400)
            result.AddError("流量泵1流量必须在0.1到400 L/min之间");
            
        if (common.FlowRate2 < 0.1 || common.FlowRate2 > 400)
            result.AddError("流量泵2流量必须在0.1到400 L/min之间");
            
        // 重复次数验证
        if (common.RepeatCount < 1 || common.RepeatCount > 999)
            result.AddError("重复次数必须在1到999之间");
            
        return result;
    }
    
    public ValidationResult ValidateConstantCurrentParameters(ConstantCurrentParameters cc)
    {
        var result = new ValidationResult();
        
        if (cc.TargetCurrent < 0 || cc.TargetCurrent > 170)
            result.AddError("目标电流必须在0到170A之间");
            
        if (cc.Duration < 1 || cc.Duration > 999999)
            result.AddError("实验持续时间必须在1到999999秒之间");
            
        if (cc.VoltageUpperLimit <= cc.VoltageLowerLimit)
            result.AddError("电压上限必须大于电压下限");
            
        if (cc.SamplingInterval < 0.1 || cc.SamplingInterval > 60)
            result.AddError("采样间隔必须在0.1到60秒之间");
            
        return result;
    }
    
    // 其他模式的验证方法...
}

## 错误处理

### 错误分类和处理策略

#### 1. 设备通讯错误
- **检测**：定期心跳检测设备连接状态
- **处理**：自动重连机制，最多重试3次
- **恢复**：记录错误日志，通知用户界面显示设备状态

#### 2. 数据库连接错误
- **检测**：数据库操作异常捕获
- **处理**：启用文件备份模式，确保数据不丢失
- **恢复**：数据库恢复后自动同步文件数据

#### 3. 实验参数超限错误
- **检测**：参数验证和实时监控
- **处理**：立即停止实验，保护设备安全
- **恢复**：记录错误原因，等待用户确认后可重新开始

#### 4. 系统崩溃恢复
- **状态保存**：每秒保存实验状态到数据库
- **恢复检测**：服务启动时检查未完成的实验
- **自动恢复**：提示用户选择是否继续之前的实验

### 错误处理接口

```csharp
public interface IErrorHandler
{
    Task HandleDeviceErrorAsync(DeviceError error);
    Task HandleDataErrorAsync(DataError error);
    Task HandleSystemErrorAsync(SystemError error);
    Task<bool> TryRecoverAsync(ErrorContext context);
}

public class ErrorRecoveryService : IErrorHandler
{
    public async Task<bool> TryRecoverExperimentAsync(Guid experimentId)
    {
        // 实验恢复逻辑
        var experiment = await _dataService.GetExperimentAsync(experimentId);
        if (experiment?.Status == ExperimentStatus.Running)
        {
            // 重新连接设备
            await _deviceManager.ReconnectAllDevicesAsync();
            
            // 恢复实验状态
            await _experimentController.ResumeExperimentAsync(experiment);
            
            return true;
        }
        return false;
    }
}
```

## 测试策略

### 单元测试
- **设备管理器测试**：模拟设备响应，测试通讯协议
- **实验控制器测试**：测试三种实验模式的逻辑
- **数据处理器测试**：测试数据验证和存储逻辑

### 集成测试
- **线程间通讯测试**：测试UI线程和后台线程的数据交换
- **数据库集成测试**：测试数据存储和查询功能
- **设备集成测试**：使用模拟设备测试完整流程

### 系统测试
- **长期运行测试**：模拟数周的连续实验
- **故障恢复测试**：模拟各种故障场景的恢复能力
- **性能测试**：测试高频数据采集的系统性能

### 测试工具和框架
- **单元测试**：MSTest + Moq
- **集成测试**：TestContainers for SQL Server
- **UI测试**：FlaUI for WPF自动化测试
- **性能测试**：NBomber for负载测试

## 实验流程设计

### 完整实验流程

基于需求13和需求14，系统实现了精确的实验时间管理和设备预热流程：

```mermaid
sequenceDiagram
    participant User as 用户
    participant UI as 用户界面
    participant Controller as 实验控制器
    participant Devices as 设备管理器
    participant DB as 数据库
    
    User->>UI: 点击开始实验
    UI->>DB: 记录实验启动请求时刻(T1)
    UI->>Controller: 发送实验开始命令
    
    Note over Controller: 设备预热阶段
    Controller->>Devices: 启动温控器和水泵
    Controller->>Devices: 监测温度和流量
    
    loop 等待设备就绪
        Devices->>Controller: 返回当前温度和流量
        Controller->>UI: 更新预热状态显示
        alt 温度±1°C且流量±5%
            Controller->>DB: 记录设备就绪时刻(T2)
            Controller->>Devices: 启动电源
            Controller->>DB: 记录实验开始时刻(T3)
            break 开始正式实验
        else 超时30分钟
            Controller->>UI: 显示设备检查提示
            break 停止启动流程
        end
    end
    
    Note over Controller: 正式实验阶段
    loop 实验数据采集
        Devices->>Controller: 采集设备数据
        Controller->>DB: 保存数据点
        Controller->>UI: 更新实时显示
    end
    
    Controller->>DB: 记录实验结束时刻(T4)
    Controller->>UI: 显示实验完成
```

### 时间管理设计

系统实现四个关键时间点的精确记录：

- **T1 (实验启动请求时刻)**：用户点击开始按钮的时刻
- **T2 (设备就绪时刻)**：温度和流量均达到设定值的时刻  
- **T3 (实验开始时刻)**：电源启动，正式开始实验的时刻
- **T4 (实验结束时刻)**：实验完成或停止的时刻

**设计决策理由**：分离预热时间和实验时间，确保实验数据的准确性和可比性。实验人员可以清楚地区分设备准备时间和实际实验时间。

### 设备就绪检测逻辑

```csharp
public class DeviceReadinessChecker
{
    private readonly double TEMPERATURE_TOLERANCE = 1.0; // ±1°C
    private readonly double FLOW_RATE_TOLERANCE = 0.05; // ±5%
    private readonly TimeSpan MAX_WAIT_TIME = TimeSpan.FromMinutes(30);
    
    public async Task<bool> WaitForDevicesReadyAsync(ExperimentConfig config)
    {
        var startTime = DateTime.Now;
        
        while (DateTime.Now - startTime < MAX_WAIT_TIME)
        {
            var currentTemp = await _temperatureController.GetCurrentTemperatureAsync();
            var currentFlow1 = await _pump1.GetFlowRateAsync();
            var currentFlow2 = await _pump2.GetFlowRateAsync();
            
            var tempReady = Math.Abs(currentTemp - config.Common.TargetTemperature) <= TEMPERATURE_TOLERANCE;
            var flow1Ready = Math.Abs(currentFlow1 - config.Common.FlowRate1) <= (config.Common.FlowRate1 * FLOW_RATE_TOLERANCE);
            var flow2Ready = Math.Abs(currentFlow2 - config.Common.FlowRate2) <= (config.Common.FlowRate2 * FLOW_RATE_TOLERANCE);
            
            if (tempReady && flow1Ready && flow2Ready)
            {
                return true;
            }
            
            // 通知UI更新预热状态
            NotifyPreheatingStatus(currentTemp, currentFlow1, currentFlow2, config);
            
            await Task.Delay(1000); // 每秒检查一次
        }
        
        return false; // 超时
    }
}
```

## 崩溃恢复设计

### 状态持久化机制

基于需求15，系统实现了完整的崩溃恢复机制：

```csharp
public class ExperimentStateManager
{
    public class ExperimentState
    {
        public Guid ExperimentId { get; set; }
        public ExperimentConfig Configuration { get; set; }
        public ExperimentStatus Status { get; set; }
        public DateTime? StartTime { get; set; }
        public DateTime? DeviceReadyTime { get; set; }
        public DateTime? ExperimentStartTime { get; set; }
        public int CurrentCycle { get; set; }
        public double CurrentProgress { get; set; }
        public Dictionary<string, object> DeviceStates { get; set; }
        public DateTime LastSaveTime { get; set; }
    }
    
    // 每秒保存实验状态
    public async Task SaveExperimentStateAsync(ExperimentState state)
    {
        state.LastSaveTime = DateTime.Now;
        
        // 保存到数据库
        await _dbContext.ExperimentStates.AddOrUpdateAsync(state);
        
        // 同时保存到本地文件作为备份
        var json = JsonSerializer.Serialize(state);
        await File.WriteAllTextAsync($"experiment_state_{state.ExperimentId}.json", json);
    }
    
    // 系统启动时检查未完成实验
    public async Task<List<ExperimentState>> GetUnfinishedExperimentsAsync()
    {
        return await _dbContext.ExperimentStates
            .Where(s => s.Status == ExperimentStatus.Running || s.Status == ExperimentStatus.Paused)
            .ToListAsync();
    }
}
```

### 恢复流程设计

```mermaid
flowchart TD
    A[系统启动] --> B[检查未完成实验]
    B --> C{发现未完成实验?}
    C -->|否| D[正常启动]
    C -->|是| E[显示恢复对话框]
    E --> F{用户选择恢复?}
    F -->|否| G[标记实验为已取消]
    F -->|是| H[重新连接设备]
    H --> I{设备连接成功?}
    I -->|否| J[显示设备连接错误]
    I -->|是| K[恢复实验状态]
    K --> L[继续实验]
    G --> D
    J --> M[提供手动重试选项]
    M --> H
```

**设计决策理由**：提供用户选择权，避免强制恢复可能导致的问题。同时提供详细的恢复状态信息，帮助用户做出正确决策。

## 用户界面安全设计

### 操作确认机制

基于需求16，系统实现了全面的操作安全机制：

```csharp
public class SafetyConfirmationService
{
    public enum OperationType
    {
        StartExperiment,
        StopExperiment,
        EmergencyStop,
        DeleteData,
        ExitSystem,
        DeleteTemplate
    }
    
    public async Task<bool> ConfirmOperationAsync(OperationType operation, string additionalInfo = null)
    {
        var message = GetConfirmationMessage(operation, additionalInfo);
        var icon = GetMessageBoxIcon(operation);
        
        var result = MessageBox.Show(message, "操作确认", 
            MessageBoxButton.YesNo, icon);
            
        // 对于危险操作，需要二次确认
        if (IsDangerousOperation(operation) && result == MessageBoxResult.Yes)
        {
            var secondConfirm = MessageBox.Show(
                "此操作不可撤销，请再次确认是否继续？", 
                "最终确认", 
                MessageBoxButton.YesNo, 
                MessageBoxImage.Warning);
                
            return secondConfirm == MessageBoxResult.Yes;
        }
        
        return result == MessageBoxResult.Yes;
    }
    
    private bool IsDangerousOperation(OperationType operation)
    {
        return operation == OperationType.EmergencyStop || 
               operation == OperationType.DeleteData;
    }
}
```

### 界面状态管理

```csharp
public class UIStateManager
{
    public enum SystemState
    {
        Idle,           // 空闲状态
        Preparing,      // 准备中（设备预热）
        Running,        // 实验运行中
        Paused,         // 实验暂停
        Stopping,       // 停止中
        Error           // 错误状态
    }
    
    public void UpdateUIState(SystemState newState)
    {
        CurrentState = newState;
        
        // 根据状态更新按钮可用性
        StartButton.IsEnabled = (newState == SystemState.Idle);
        StopButton.IsEnabled = (newState == SystemState.Running || newState == SystemState.Paused);
        EmergencyStopButton.IsEnabled = (newState != SystemState.Idle);
        ParameterPanel.IsEnabled = (newState == SystemState.Idle);
        
        // 更新状态指示器
        UpdateStatusIndicator(newState);
        
        // 更新状态栏文本
        UpdateStatusText(newState);
    }
    
    private void UpdateStatusIndicator(SystemState state)
    {
        var (color, text) = state switch
        {
            SystemState.Idle => (Brushes.Gray, "就绪"),
            SystemState.Preparing => (Brushes.Orange, "准备中"),
            SystemState.Running => (Brushes.Green, "运行中"),
            SystemState.Paused => (Brushes.Yellow, "已暂停"),
            SystemState.Stopping => (Brushes.Red, "停止中"),
            SystemState.Error => (Brushes.Red, "错误"),
            _ => (Brushes.Gray, "未知")
        };
        
        StatusIndicator.Fill = color;
        StatusText.Text = text;
    }
}
```

## 数据采集和实时性设计

### 数据采集频率控制

基于需求19，系统实现了灵活的数据采集频率控制：

```csharp
public class DataAcquisitionController
{
    private readonly Timer _acquisitionTimer;
    private readonly ConcurrentQueue<DataPoint> _dataBuffer;
    private readonly SemaphoreSlim _acquisitionSemaphore;
    private double _samplingInterval = 1.0; // 默认1秒
    private DateTime _lastAcquisitionTime;
    private int _consecutiveFailures = 0;
    
    public double SamplingInterval 
    { 
        get => _samplingInterval;
        set 
        {
            if (value < 0.1 || value > 60)
                throw new ArgumentOutOfRangeException("采样间隔必须在0.1到60秒之间");
            
            _samplingInterval = value;
            UpdateTimerInterval();
        }
    }
    
    public async Task<bool> AcquireDataPointAsync()
    {
        try
        {
            var startTime = DateTime.Now;
            
            // 并行采集所有设备数据
            var tasks = new[]
            {
                _powerSupply.GetVoltageAsync(),
                _powerSupply.GetCurrentAsync(),
                _temperatureController.GetCurrentTemperatureAsync(),
                _pump1.GetFlowRateAsync(),
                _pump2.GetFlowRateAsync()
            };
            
            var results = await Task.WhenAll(tasks);
            
            var dataPoint = new DataPoint
            {
                Timestamp = startTime,
                ElapsedSeconds = (startTime - _experimentStartTime).TotalSeconds,
                Voltage = results[0],
                Current = results[1],
                Temperature = results[2],
                FlowRate1 = results[3],
                FlowRate2 = results[4]
            };
            
            // 检查采集延迟
            var acquisitionDelay = (DateTime.Now - startTime).TotalMilliseconds;
            if (acquisitionDelay > 100)
            {
                NotifyDelayWarning(acquisitionDelay);
            }
            
            // 缓冲数据点
            _dataBuffer.Enqueue(dataPoint);
            
            // 通知UI更新（异步，不阻塞采集）
            _ = Task.Run(() => NotifyDataPointReceived(dataPoint));
            
            // 保存到数据库（异步，不阻塞采集）
            _ = Task.Run(() => SaveDataPointAsync(dataPoint));
            
            _consecutiveFailures = 0;
            return true;
        }
        catch (Exception ex)
        {
            _consecutiveFailures++;
            LogError($"数据采集失败: {ex.Message}");
            
            if (_consecutiveFailures >= 3)
            {
                await StopExperimentDueToDataFailureAsync();
            }
            
            return false;
        }
    }
    
    private async Task SaveDataPointAsync(DataPoint dataPoint)
    {
        try
        {
            var saveStartTime = DateTime.Now;
            await _dataRepository.SaveDataPointAsync(dataPoint);
            
            var saveDelay = (DateTime.Now - saveStartTime).TotalMilliseconds;
            if (saveDelay > 500)
            {
                NotifyDatabaseDelayWarning(saveDelay);
            }
        }
        catch (Exception ex)
        {
            // 启用文件备份模式
            await SaveToFileBackupAsync(dataPoint);
            LogError($"数据库保存失败，已启用文件备份: {ex.Message}");
        }
    }
}
```

### 数据缓冲机制

```csharp
public class DataBufferManager
{
    private readonly ConcurrentQueue<DataPoint> _highFrequencyBuffer;
    private readonly Timer _batchProcessTimer;
    private readonly int _maxBufferSize = 1000;
    
    public void BufferDataPoint(DataPoint dataPoint)
    {
        _highFrequencyBuffer.Enqueue(dataPoint);
        
        // 防止缓冲区溢出
        while (_highFrequencyBuffer.Count > _maxBufferSize)
        {
            _highFrequencyBuffer.TryDequeue(out _);
        }
    }
    
    // 批量处理缓冲数据，减少UI更新频率
    private async void ProcessBufferedData(object sender, ElapsedEventArgs e)
    {
        var batchData = new List<DataPoint>();
        
        // 取出最近的数据点
        while (batchData.Count < 10 && _highFrequencyBuffer.TryDequeue(out var dataPoint))
        {
            batchData.Add(dataPoint);
        }
        
        if (batchData.Any())
        {
            await NotifyUIBatchUpdate(batchData);
        }
    }
}
```

## 设备通讯协议实现

### 具体设备驱动实现

基于需求10和需求20，系统实现了具体的设备通讯协议：

```csharp
// 爱德克斯IT-M3901D电源驱动
public class IttechPowerSupplyDriver : IPowerSupply
{
    private readonly TcpClient _tcpClient;
    private readonly NetworkStream _networkStream;
    private string _ipAddress;
    private int _port = 30000; // 默认端口
    
    public async Task<bool> ConnectAsync()
    {
        try
        {
            await _tcpClient.ConnectAsync(_ipAddress, _port);
            _networkStream = _tcpClient.GetStream();
            
            // 发送设备识别命令
            var response = await SendCommandAsync("*IDN?");
            if (response.Contains("IT-M3901D"))
            {
                Status = DeviceStatus.Connected;
                return true;
            }
            
            return false;
        }
        catch (Exception ex)
        {
            LogError($"电源连接失败: {ex.Message}");
            return false;
        }
    }
    
    public async Task<bool> SetVoltageAsync(double voltage)
    {
        if (voltage < 0 || voltage > 10)
            throw new ArgumentOutOfRangeException("电压必须在0-10V范围内");
            
        var command = $"VOLT {voltage:F3}";
        var response = await SendCommandAsync(command);
        return response == "OK";
    }
    
    public async Task<double> GetVoltageAsync()
    {
        var response = await SendCommandAsync("VOLT?");
        return double.Parse(response);
    }
    
    private async Task<string> SendCommandAsync(string command)
    {
        var data = Encoding.ASCII.GetBytes(command + "\n");
        await _networkStream.WriteAsync(data, 0, data.Length);
        
        var buffer = new byte[1024];
        var bytesRead = await _networkStream.ReadAsync(buffer, 0, buffer.Length);
        return Encoding.ASCII.GetString(buffer, 0, bytesRead).Trim();
    }
}

// 卡川DI Pump550流量泵驱动
public class KaichuanPumpDriver : IFlowPump
{
    private readonly SerialPort _serialPort;
    private readonly byte _slaveAddress;
    
    public KaichuanPumpDriver(string portName, byte slaveAddress)
    {
        _serialPort = new SerialPort(portName, 9600, Parity.None, 8, StopBits.One);
        _slaveAddress = slaveAddress;
    }
    
    public async Task<bool> ConnectAsync()
    {
        try
        {
            _serialPort.Open();
            
            // 测试通讯
            var response = await ReadHoldingRegistersAsync(0x0001, 1);
            if (response != null)
            {
                Status = DeviceStatus.Connected;
                return true;
            }
            
            return false;
        }
        catch (Exception ex)
        {
            LogError($"流量泵连接失败: {ex.Message}");
            return false;
        }
    }
    
    public async Task<bool> SetFlowRateAsync(double flowRate)
    {
        if (flowRate < 0.1 || flowRate > 400)
            throw new ArgumentOutOfRangeException("流量必须在0.1-400 L/min范围内");
            
        // 将流量值转换为设备寄存器值
        var registerValue = (ushort)(flowRate * 10);
        return await WriteSingleRegisterAsync(0x0010, registerValue);
    }
    
    public async Task<double> GetFlowRateAsync()
    {
        var response = await ReadHoldingRegistersAsync(0x0011, 1);
        return response?[0] / 10.0 ?? 0.0;
    }
    
    private async Task<bool> WriteSingleRegisterAsync(ushort address, ushort value)
    {
        var request = new byte[]
        {
            _slaveAddress,
            0x06, // 功能码：写单个寄存器
            (byte)(address >> 8),
            (byte)(address & 0xFF),
            (byte)(value >> 8),
            (byte)(value & 0xFF)
        };
        
        var crc = CalculateCRC(request);
        var frame = request.Concat(crc).ToArray();
        
        _serialPort.Write(frame, 0, frame.Length);
        
        // 等待响应
        await Task.Delay(100);
        var responseLength = _serialPort.BytesToRead;
        if (responseLength >= 8)
        {
            var response = new byte[responseLength];
            _serialPort.Read(response, 0, responseLength);
            return ValidateResponse(response);
        }
        
        return false;
    }
    
    private async Task<ushort[]> ReadHoldingRegistersAsync(ushort address, ushort count)
    {
        var request = new byte[]
        {
            _slaveAddress,
            0x03, // 功能码：读保持寄存器
            (byte)(address >> 8),
            (byte)(address & 0xFF),
            (byte)(count >> 8),
            (byte)(count & 0xFF)
        };
        
        var crc = CalculateCRC(request);
        var frame = request.Concat(crc).ToArray();
        
        _serialPort.Write(frame, 0, frame.Length);
        
        // 等待响应
        await Task.Delay(100);
        var responseLength = _serialPort.BytesToRead;
        if (responseLength >= 5 + count * 2)
        {
            var response = new byte[responseLength];
            _serialPort.Read(response, 0, responseLength);
            
            if (ValidateResponse(response))
            {
                var values = new ushort[count];
                for (int i = 0; i < count; i++)
                {
                    values[i] = (ushort)((response[3 + i * 2] << 8) | response[4 + i * 2]);
                }
                return values;
            }
        }
        
        return null;
    }
    
    private byte[] CalculateCRC(byte[] data)
    {
        ushort crc = 0xFFFF;
        foreach (byte b in data)
        {
            crc ^= b;
            for (int i = 0; i < 8; i++)
            {
                if ((crc & 1) != 0)
                    crc = (ushort)((crc >> 1) ^ 0xA001);
                else
                    crc >>= 1;
            }
        }
        return new byte[] { (byte)(crc & 0xFF), (byte)(crc >> 8) };
    }
}

// 宇电MK008温控器驱动
public class YudianTemperatureControllerDriver : ITemperatureController
{
    private readonly SerialPort _serialPort;
    private readonly byte _slaveAddress = 3;
    
    public YudianTemperatureControllerDriver(string portName)
    {
        _serialPort = new SerialPort(portName, 9600, Parity.None, 8, StopBits.One);
    }
    
    public async Task<bool> SetTargetTemperatureAsync(double temperature)
    {
        if (temperature < 20 || temperature > 90)
            throw new ArgumentOutOfRangeException("温度必须在20-90°C范围内");
            
        // 温度值需要乘以10发送给设备
        var registerValue = (ushort)(temperature * 10);
        return await WriteSingleRegisterAsync(0x0001, registerValue);
    }
    
    public async Task<double> GetCurrentTemperatureAsync()
    {
        var response = await ReadHoldingRegistersAsync(0x0000, 1);
        return response?[0] / 10.0 ?? 0.0;
    }
    
    public async Task<double> GetTargetTemperatureAsync()
    {
        var response = await ReadHoldingRegistersAsync(0x0001, 1);
        return response?[0] / 10.0 ?? 0.0;
    }
    
    // Modbus RTU实现方法与流量泵类似...
}
```

## 数据导出功能设计

### 数据导出服务

基于需求7，系统实现了灵活的数据导出功能：

```csharp
public interface IDataExportService
{
    Task<bool> ExportToCsvAsync(ExportRequest request);
    Task<bool> ExportToExcelAsync(ExportRequest request);
    Task<ExportPreview> PreviewExportDataAsync(ExportRequest request);
}

public class ExportRequest
{
    public Guid ExperimentId { get; set; }
    public DateTime? StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public string FilePath { get; set; }
    public bool IncludeDeviceStatus { get; set; } = true;
    public bool UseRelativeTime { get; set; } = true; // 使用相对于实验开始的秒数
    public List<string> SelectedColumns { get; set; } = new();
}

public class DataExportService : IDataExportService
{
    public async Task<bool> ExportToCsvAsync(ExportRequest request)
    {
        try
        {
            var data = await GetExportDataAsync(request);
            var csv = new StringBuilder();
            
            // 添加表头
            var headers = GetColumnHeaders(request);
            csv.AppendLine(string.Join(",", headers));
            
            // 添加数据行
            foreach (var row in data)
            {
                var values = new List<string>();
                
                if (request.UseRelativeTime)
                {
                    values.Add(row.ElapsedSeconds.ToString("F3")); // 相对时间（秒）
                }
                else
                {
                    values.Add(row.Timestamp.ToString("yyyy-MM-dd HH:mm:ss.fff")); // 绝对时间
                }
                
                values.Add(row.Voltage.ToString("F3"));
                values.Add(row.Current.ToString("F2"));
                
                if (row.Temperature.HasValue)
                    values.Add(row.Temperature.Value.ToString("F2"));
                else
                    values.Add("");
                    
                if (row.FlowRate1.HasValue)
                    values.Add(row.FlowRate1.Value.ToString("F2"));
                else
                    values.Add("");
                    
                if (row.FlowRate2.HasValue)
                    values.Add(row.FlowRate2.Value.ToString("F2"));
                else
                    values.Add("");
                
                csv.AppendLine(string.Join(",", values));
            }
            
            await File.WriteAllTextAsync(request.FilePath, csv.ToString(), Encoding.UTF8);
            return true;
        }
        catch (Exception ex)
        {
            LogError($"CSV导出失败: {ex.Message}");
            return false;
        }
    }
    
    public async Task<bool> ExportToExcelAsync(ExportRequest request)
    {
        try
        {
            using var workbook = new XLWorkbook();
            var worksheet = workbook.Worksheets.Add("实验数据");
            
            var data = await GetExportDataAsync(request);
            var headers = GetColumnHeaders(request);
            
            // 添加表头
            for (int i = 0; i < headers.Count; i++)
            {
                worksheet.Cell(1, i + 1).Value = headers[i];
                worksheet.Cell(1, i + 1).Style.Font.Bold = true;
            }
            
            // 添加数据
            int row = 2;
            foreach (var dataPoint in data)
            {
                int col = 1;
                
                if (request.UseRelativeTime)
                {
                    worksheet.Cell(row, col++).Value = dataPoint.ElapsedSeconds;
                }
                else
                {
                    worksheet.Cell(row, col++).Value = dataPoint.Timestamp;
                }
                
                worksheet.Cell(row, col++).Value = dataPoint.Voltage;
                worksheet.Cell(row, col++).Value = dataPoint.Current;
                worksheet.Cell(row, col++).Value = dataPoint.Temperature;
                worksheet.Cell(row, col++).Value = dataPoint.FlowRate1;
                worksheet.Cell(row, col++).Value = dataPoint.FlowRate2;
                
                row++;
            }
            
            // 自动调整列宽
            worksheet.Columns().AdjustToContents();
            
            workbook.SaveAs(request.FilePath);
            return true;
        }
        catch (Exception ex)
        {
            LogError($"Excel导出失败: {ex.Message}");
            return false;
        }
    }
    
    private async Task<List<DataPoint>> GetExportDataAsync(ExportRequest request)
    {
        var query = _dbContext.DataPoints
            .Where(dp => dp.ExperimentId == request.ExperimentId);
            
        if (request.StartTime.HasValue)
            query = query.Where(dp => dp.Timestamp >= request.StartTime.Value);
            
        if (request.EndTime.HasValue)
            query = query.Where(dp => dp.Timestamp <= request.EndTime.Value);
            
        return await query.OrderBy(dp => dp.Timestamp).ToListAsync();
    }
    
    private List<string> GetColumnHeaders(ExportRequest request)
    {
        var headers = new List<string>();
        
        if (request.UseRelativeTime)
            headers.Add("时间(秒)");
        else
            headers.Add("时间戳");
            
        headers.AddRange(new[] { "电压(V)", "电流(A)", "温度(°C)", "流量1(L/min)", "流量2(L/min)" });
        
        return headers;
    }
}
```

## 安全保护系统设计

### 综合安全监控

基于需求5，系统实现了多层次的安全保护：

```csharp
public class SafetyMonitoringService
{
    private readonly Timer _safetyCheckTimer;
    private readonly Dictionary<string, DateTime> _lastDeviceResponse;
    private readonly object _safetyLock = new object();
    
    public SafetyMonitoringService()
    {
        _safetyCheckTimer = new Timer(PerformSafetyCheck, null, TimeSpan.Zero, TimeSpan.FromMilliseconds(500));
        _lastDeviceResponse = new Dictionary<string, DateTime>();
    }
    
    private async void PerformSafetyCheck(object state)
    {
        try
        {
            await CheckParameterLimitsAsync();
            await CheckDeviceCommunicationAsync();
            await CheckTemperatureLimitsAsync();
            await CheckEmergencyStopAsync();
        }
        catch (Exception ex)
        {
            LogError($"安全检查异常: {ex.Message}");
        }
    }
    
    private async Task CheckParameterLimitsAsync()
    {
        if (_currentExperiment == null) return;
        
        var currentVoltage = await _powerSupply.GetVoltageAsync();
        var currentCurrent = await _powerSupply.GetCurrentAsync();
        
        // 检查电压限制
        if (currentVoltage < 0 || currentVoltage > 10)
        {
            await TriggerSafetyStopAsync($"电压超限: {currentVoltage:F3}V");
            return;
        }
        
        // 检查电流限制
        if (currentCurrent < 0 || currentCurrent > 170)
        {
            await TriggerSafetyStopAsync($"电流超限: {currentCurrent:F2}A");
            return;
        }
        
        // 检查实验模式特定限制
        switch (_currentExperiment.Type)
        {
            case ExperimentType.ConstantCurrent:
                var ccParams = _currentExperiment.ConstantCurrent;
                if (currentVoltage > ccParams.VoltageUpperLimit || 
                    currentVoltage < ccParams.VoltageLowerLimit)
                {
                    await TriggerSafetyStopAsync($"恒流模式电压保护触发: {currentVoltage:F3}V");
                }
                break;
                
            case ExperimentType.ConstantVoltage:
                var cvParams = _currentExperiment.ConstantVoltage;
                if (currentCurrent > cvParams.CurrentUpperLimit || 
                    currentCurrent < cvParams.CurrentLowerLimit)
                {
                    await TriggerSafetyStopAsync($"恒压模式电流保护触发: {currentCurrent:F2}A");
                }
                break;
                
            case ExperimentType.LinearVoltageRamp:
                var rampParams = _currentExperiment.LinearVoltageRamp;
                if (currentCurrent > rampParams.CurrentUpperLimit || 
                    currentCurrent < rampParams.CurrentLowerLimit)
                {
                    await TriggerSafetyStopAsync($"线性提升模式电流保护触发: {currentCurrent:F2}A");
                }
                break;
        }
    }
    
    private async Task CheckDeviceCommunicationAsync()
    {
        var devices = new[] { "PowerSupply", "Pump1", "Pump2", "TemperatureController" };
        
        foreach (var deviceName in devices)
        {
            if (_lastDeviceResponse.TryGetValue(deviceName, out var lastResponse))
            {
                var timeSinceLastResponse = DateTime.Now - lastResponse;
                if (timeSinceLastResponse.TotalSeconds > 5)
                {
                    await TriggerSafetyStopAsync($"设备通讯中断: {deviceName}，已超过5秒无响应");
                    return;
                }
            }
        }
    }
    
    private async Task CheckTemperatureLimitsAsync()
    {
        var currentTemp = await _temperatureController.GetCurrentTemperatureAsync();
        
        if (currentTemp > 95)
        {
            await TriggerSafetyStopAsync($"温度过高警告: {currentTemp:F1}°C，已超过95°C安全限制");
        }
    }
    
    private async Task CheckEmergencyStopAsync()
    {
        // 检查紧急停止按钮状态
        if (_emergencyStopRequested)
        {
            await TriggerEmergencyStopAsync();
        }
    }
    
    private async Task TriggerSafetyStopAsync(string reason)
    {
        LogWarning($"安全保护触发: {reason}");
        
        // 在1秒内停止实验
        var stopTask = StopExperimentAsync();
        var timeoutTask = Task.Delay(1000);
        
        var completedTask = await Task.WhenAny(stopTask, timeoutTask);
        
        if (completedTask == timeoutTask)
        {
            LogError("安全停止超时，强制切断电源");
            await _powerSupply.SetOutputAsync(false);
        }
        
        // 通知用户界面
        NotifySafetyAlert(reason);
    }
    
    private async Task TriggerEmergencyStopAsync()
    {
        LogWarning("紧急停止触发");
        
        // 在500ms内立即切断电源
        var emergencyStopTask = _powerSupply.SetOutputAsync(false);
        var timeoutTask = Task.Delay(500);
        
        var completedTask = await Task.WhenAny(emergencyStopTask, timeoutTask);
        
        if (completedTask == timeoutTask)
        {
            LogError("紧急停止超时，可能存在硬件故障");
        }
        
        // 停止所有设备
        await Task.WhenAll(
            _pump1.StopAsync(),
            _pump2.StopAsync(),
            _temperatureController.StopHeatingAsync()
        );
        
        NotifyEmergencyStop();
        _emergencyStopRequested = false;
    }
    
    public void UpdateDeviceResponseTime(string deviceName)
    {
        lock (_safetyLock)
        {
            _lastDeviceResponse[deviceName] = DateTime.Now;
        }
    }
}
```

## 界面原型设计需求

### HTML原型设计

基于需求21，系统需要创建界面原型用于用户沟通：

```csharp
public class UIPrototypeGenerator
{
    public async Task<string> GeneratePrototypeHtmlAsync()
    {
        var html = @"
<!DOCTYPE html>
<html lang='zh-CN'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>PEM电解槽实验测试系统 - 界面原型</title>
    <style>
        body { font-family: 'Microsoft YaHei', sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: #2c3e50; color: white; padding: 15px 20px; border-radius: 8px 8px 0 0; }
        .main-content { display: flex; padding: 20px; gap: 20px; }
        .left-panel { flex: 1; }
        .right-panel { flex: 2; }
        .section { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 6px; margin-bottom: 20px; }
        .section-header { background: #e9ecef; padding: 10px 15px; border-bottom: 1px solid #dee2e6; font-weight: bold; }
        .section-content { padding: 15px; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-control { width: 100%; padding: 8px; border: 1px solid #ced4da; border-radius: 4px; }
        .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-ready { background: #28a745; }
        .status-running { background: #007bff; }
        .status-error { background: #dc3545; }
        .chart-container { height: 300px; background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: #6c757d; }
        .device-status { display: flex; justify-content: space-between; align-items: center; padding: 10px; background: white; border: 1px solid #dee2e6; border-radius: 4px; margin-bottom: 10px; }
        .parameter-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; }
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>PEM电解槽实验测试系统</h1>
            <div style='display: flex; justify-content: space-between; align-items: center;'>
                <div>
                    <span class='status-indicator status-ready'></span>
                    系统就绪
                </div>
                <div>
                    当前时间: <span id='current-time'></span>
                </div>
            </div>
        </div>
        
        <div class='main-content'>
            <div class='left-panel'>
                <!-- 实验模式选择 -->
                <div class='section'>
                    <div class='section-header'>实验模式选择</div>
                    <div class='section-content'>
                        <div class='form-group'>
                            <label><input type='radio' name='experiment-mode' value='constant-current' checked> 恒定电流模式</label>
                            <label><input type='radio' name='experiment-mode' value='constant-voltage'> 恒定电压模式</label>
                            <label><input type='radio' name='experiment-mode' value='linear-ramp'> 线性提升电压模式</label>
                        </div>
                    </div>
                </div>
                
                <!-- 参数设置 -->
                <div class='section'>
                    <div class='section-header'>实验参数设置</div>
                    <div class='section-content'>
                        <div class='parameter-grid'>
                            <div class='form-group'>
                                <label>目标电流 (A)</label>
                                <input type='number' class='form-control' value='50.00' step='0.01' min='0' max='170'>
                            </div>
                            <div class='form-group'>
                                <label>实验时间 (秒)</label>
                                <input type='number' class='form-control' value='3600' min='1' max='999999'>
                            </div>
                            <div class='form-group'>
                                <label>目标温度 (°C)</label>
                                <input type='number' class='form-control' value='60.0' step='0.1' min='20' max='90'>
                            </div>
                            <div class='form-group'>
                                <label>流量1 (L/min)</label>
                                <input type='number' class='form-control' value='10.0' step='0.1' min='0.1' max='400'>
                            </div>
                            <div class='form-group'>
                                <label>流量2 (L/min)</label>
                                <input type='number' class='form-control' value='15.0' step='0.1' min='0.1' max='400'>
                            </div>
                            <div class='form-group'>
                                <label>循环次数</label>
                                <input type='number' class='form-control' value='1' min='1' max='999'>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 参数模板 -->
                <div class='section'>
                    <div class='section-header'>参数模板</div>
                    <div class='section-content'>
                        <div class='form-group'>
                            <select class='form-control'>
                                <option>选择模板...</option>
                                <option>标准测试模板</option>
                                <option>高温测试模板</option>
                                <option>长期稳定性测试</option>
                            </select>
                        </div>
                        <div style='display: flex; gap: 10px;'>
                            <button class='btn btn-primary'>加载模板</button>
                            <button class='btn btn-success'>保存为模板</button>
                        </div>
                    </div>
                </div>
                
                <!-- 控制按钮 -->
                <div class='section'>
                    <div class='section-header'>实验控制</div>
                    <div class='section-content'>
                        <div style='display: flex; gap: 10px; flex-wrap: wrap;'>
                            <button class='btn btn-success' onclick='startExperiment()'>开始实验</button>
                            <button class='btn btn-warning' disabled>暂停实验</button>
                            <button class='btn btn-primary' disabled>停止实验</button>
                            <button class='btn btn-danger' onclick='emergencyStop()'>紧急停止</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class='right-panel'>
                <!-- 实时数据显示 -->
                <div class='section'>
                    <div class='section-header'>实时数据监控</div>
                    <div class='section-content'>
                        <div class='parameter-grid'>
                            <div>
                                <strong>电压:</strong> <span id='voltage-value'>0.000 V</span>
                            </div>
                            <div>
                                <strong>电流:</strong> <span id='current-value'>0.00 A</span>
                            </div>
                            <div>
                                <strong>温度:</strong> <span id='temperature-value'>25.0 °C</span>
                            </div>
                            <div>
                                <strong>实验时间:</strong> <span id='experiment-time'>00:00:00</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 实时图表 -->
                <div class='section'>
                    <div class='section-header'>实时曲线图</div>
                    <div class='section-content'>
                        <div class='chart-container'>
                            <div>实时曲线图 (电压-时间)</div>
                        </div>
                    </div>
                </div>
                
                <!-- 设备状态 -->
                <div class='section'>
                    <div class='section-header'>设备状态监控</div>
                    <div class='section-content'>
                        <div class='device-status'>
                            <span><span class='status-indicator status-ready'></span>电源 (IT-M3901D)</span>
                            <span>在线</span>
                        </div>
                        <div class='device-status'>
                            <span><span class='status-indicator status-ready'></span>流量泵1 (DI Pump550)</span>
                            <span>在线 - 10.0 L/min</span>
                        </div>
                        <div class='device-status'>
                            <span><span class='status-indicator status-ready'></span>流量泵2 (DI Pump550)</span>
                            <span>在线 - 15.0 L/min</span>
                        </div>
                        <div class='device-status'>
                            <span><span class='status-indicator status-ready'></span>温控器 (MK008)</span>
                            <span>在线 - 60.0°C</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 更新当前时间
        function updateTime() {
            document.getElementById('current-time').textContent = new Date().toLocaleString();
        }
        setInterval(updateTime, 1000);
        updateTime();
        
        // 模拟开始实验
        function startExperiment() {
            if (confirm('确定要开始实验吗？')) {
                alert('实验开始！系统正在预热设备...');
                // 这里会触发实际的实验开始流程
            }
        }
        
        // 模拟紧急停止
        function emergencyStop() {
            if (confirm('确定要执行紧急停止吗？此操作将立即切断所有设备电源！')) {
                alert('紧急停止已执行！所有设备已安全停止。');
            }
        }
        
        // 模拟实时数据更新
        function simulateDataUpdate() {
            const voltage = (Math.random() * 2 + 1).toFixed(3);
            const current = (Math.random() * 20 + 40).toFixed(2);
            const temperature = (Math.random() * 5 + 58).toFixed(1);
            
            document.getElementById('voltage-value').textContent = voltage + ' V';
            document.getElementById('current-value').textContent = current + ' A';
            document.getElementById('temperature-value').textContent = temperature + ' °C';
        }
        
        // 每2秒更新一次模拟数据
        setInterval(simulateDataUpdate, 2000);
    </script>
</body>
</html>";
        
        return html;
    }
}
```

## 设计确认文档生成

### 系统设计方案文档

基于需求22，系统需要生成设计确认文档：

```csharp
public class DesignDocumentGenerator
{
    public async Task<string> GenerateDesignConfirmationDocumentAsync()
    {
        var document = new StringBuilder();
        
        document.AppendLine("# PEM电解槽实验测试系统设计方案");
        document.AppendLine();
        document.AppendLine("## 前期沟通汇总");
        document.AppendLine();
        document.AppendLine("### 系统需求概述");
        document.AppendLine("- 支持三种实验模式：恒定电流、恒定电压、线性提升电压");
        document.AppendLine("- 高精度数据采集：电压精度0.001V，电流精度0.01A");
        document.AppendLine("- 实时数据监控和长期数据存储");
        document.AppendLine("- 多设备协调控制：电源、流量泵、温控器");
        document.AppendLine("- 安全保护机制和故障恢复能力");
        document.AppendLine();
        
        document.AppendLine("### 关键设计决策");
        document.AppendLine("1. **架构选择**：单进程多线程架构，确保UI响应性和数据采集连续性");
        document.AppendLine("2. **数据存储**：SQL Server主存储 + 文件备份，保证数据安全");
        document.AppendLine("3. **设备通讯**：支持以太网和Modbus RTU多种协议");
        document.AppendLine("4. **时间管理**：四时间点精确记录（T1-T4），区分预热和实验时间");
        document.AppendLine("5. **安全保护**：多层次安全监控，500ms紧急停止响应");
        document.AppendLine();
        
        document.AppendLine("## 界面设计说明");
        document.AppendLine();
        document.AppendLine("### 主界面布局");
        document.AppendLine("- **左侧面板**：实验模式选择、参数设置、模板管理、控制按钮");
        document.AppendLine("- **右侧面板**：实时数据显示、曲线图、设备状态监控");
        document.AppendLine("- **顶部状态栏**：系统状态指示、当前时间显示");
        document.AppendLine();
        
        document.AppendLine("### 用户交互流程");
        document.AppendLine("1. **实验准备**：选择模式 → 设置参数 → 验证参数 → 加载模板（可选）");
        document.AppendLine("2. **实验执行**：开始实验 → 设备预热 → 正式实验 → 实时监控");
        document.AppendLine("3. **数据管理**：实时保存 → 导出数据 → 历史查询");
        document.AppendLine();
        
        document.AppendLine("## 实验流程描述");
        document.AppendLine();
        document.AppendLine("### 完整实验流程");
        document.AppendLine("```");
        document.AppendLine("用户点击开始 → 记录T1时刻 → 启动温控器和水泵 → 等待设备就绪");
        document.AppendLine("↓");
        document.AppendLine("设备达到设定值 → 记录T2时刻 → 启动电源 → 记录T3时刻");
        document.AppendLine("↓");
        document.AppendLine("开始数据采集 → 实时显示和保存 → 实验完成 → 记录T4时刻");
        document.AppendLine("```");
        document.AppendLine();
        
        document.AppendLine("### 三种实验模式详细说明");
        document.AppendLine();
        document.AppendLine("#### 1. 恒定电流模式");
        document.AppendLine("- **控制目标**：保持电流恒定");
        document.AppendLine("- **监测参数**：电压随时间变化");
        document.AppendLine("- **显示图表**：电压-时间曲线");
        document.AppendLine("- **保护机制**：电压上下限保护");
        document.AppendLine();
        
        document.AppendLine("#### 2. 恒定电压模式");
        document.AppendLine("- **控制目标**：保持电压恒定");
        document.AppendLine("- **监测参数**：电流随时间变化");
        document.AppendLine("- **显示图表**：电流-时间曲线");
        document.AppendLine("- **保护机制**：电流上下限保护");
        document.AppendLine();
        
        document.AppendLine("#### 3. 线性提升电压模式");
        document.AppendLine("- **控制目标**：按设定斜率线性提升电压");
        document.AppendLine("- **监测参数**：电流随电压变化");
        document.AppendLine("- **显示图表**：电流-电压曲线");
        document.AppendLine("- **保护机制**：电流上下限保护");
        document.AppendLine();
        
        document.AppendLine("## 技术规格确认");
        document.AppendLine();
        document.AppendLine("### 设备连接规格");
        document.AppendLine("- **电源**：爱德克斯IT-M3901D，以太网连接，专有协议");
        document.AppendLine("- **流量泵**：卡川DI Pump550 x2，Modbus RTU，RS-485，9600波特率");
        document.AppendLine("- **温控器**：宇电MK008，Modbus RTU，9600波特率");
        document.AppendLine();
        
        document.AppendLine("### 数据精度规格");
        document.AppendLine("- **电压**：显示精度0.001V，设置精度0.001V，范围0-10V");
        document.AppendLine("- **电流**：显示精度0.01A，设置精度0.01A，范围0-170A");
        document.AppendLine("- **温度**：显示精度0.1°C，范围20-90°C");
        document.AppendLine("- **流量**：显示精度0.1L/min，范围0.1-400L/min");
        document.AppendLine();
        
        document.AppendLine("### 安全保护规格");
        document.AppendLine("- **参数超限检测**：1秒内响应");
        document.AppendLine("- **紧急停止响应**：500ms内切断电源");
        document.AppendLine("- **通讯中断检测**：5秒超时");
        document.AppendLine("- **温度保护**：95°C自动停止");
        document.AppendLine();
        
        document.AppendLine("## 确认事项");
        document.AppendLine();
        document.AppendLine("请实验人员确认以下设计要点：");
        document.AppendLine();
        document.AppendLine("- [ ] 界面布局是否符合操作习惯");
        document.AppendLine("- [ ] 实验流程是否满足实际需求");
        document.AppendLine("- [ ] 参数设置范围是否合适");
        document.AppendLine("- [ ] 安全保护机制是否充分");
        document.AppendLine("- [ ] 数据导出格式是否满足分析需求");
        document.AppendLine("- [ ] 设备连接方式是否正确");
        document.AppendLine();
        
        document.AppendLine("## 后续开发计划");
        document.AppendLine();
        document.AppendLine("确认设计方案后，将按以下顺序进行开发：");
        document.AppendLine("1. 数据库和核心数据模型");
        document.AppendLine("2. 设备通讯驱动");
        document.AppendLine("3. 实验控制逻辑");
        document.AppendLine("4. 用户界面实现");
        document.AppendLine("5. 系统集成和测试");
        document.AppendLine();
        
        return document.ToString();
    }
}
```

**设计决策理由**：

1. **多时间点记录设计**：为了精确区分设备预热时间和实际实验时间，便于实验数据分析和设备性能评估。

2. **多线程架构选择**：确保长期实验过程中UI始终保持响应，同时保证数据采集的连续性和稳定性。

3. **双重数据保护机制**：数据库主存储配合文件备份，最大程度防止长期实验中的数据丢失。

4. **分层安全保护设计**：从参数验证到实时监控再到紧急停止，提供多层次的安全保障。

5. **灵活的数据导出设计**：支持相对时间和绝对时间两种格式，满足不同的数据分析需求。

这个更新后的设计文档全面覆盖了所有22个需求，并提供了详细的技术实现方案和设计决策说明。
```

## 实时参数调整设计

### 参数调整接口

基于需求12和需求18，系统支持实验过程中的实时参数调整：

```csharp
public class RuntimeParameterController
{
    public class ParameterChangeRecord
    {
        public DateTime Timestamp { get; set; }
        public string ParameterName { get; set; }
        public double OldValue { get; set; }
        public double NewValue { get; set; }
        public string Reason { get; set; }
    }
    
    public async Task<bool> UpdateTemperatureAsync(double newTemperature)
    {
        // 参数范围验证
        if (newTemperature < 20 || newTemperature > 90)
        {
            throw new ArgumentOutOfRangeException("温度必须在20°C到90°C之间");
        }
        
        var oldValue = await _temperatureController.GetTargetTemperatureAsync();
        
        // 发送新设定值到设备
        var success = await _temperatureController.SetTargetTemperatureAsync(newTemperature);
        
        if (success)
        {
            // 记录参数变更
            var record = new ParameterChangeRecord
            {
                Timestamp = DateTime.Now,
                ParameterName = "TargetTemperature",
                OldValue = oldValue,
                NewValue = newTemperature,
                Reason = "用户手动调整"
            };
            
            await SaveParameterChangeAsync(record);
            
            // 在数据记录中标记变更
            await MarkParameterChangeInDataAsync(record);
        }
        
        return success;
    }
    
    // 参数变更历史查询
    public async Task<List<ParameterChangeRecord>> GetParameterHistoryAsync(
        Guid experimentId, 
        DateTime? startTime = null, 
        DateTime? endTime = null)
    {
        return await _dbContext.ParameterChanges
            .Where(p => p.ExperimentId == experimentId)
            .Where(p => !startTime.HasValue || p.Timestamp >= startTime)
            .Where(p => !endTime.HasValue || p.Timestamp <= endTime)
            .OrderBy(p => p.Timestamp)
            .ToListAsync();
    }
}
```

**设计决策理由**：实时参数调整功能允许实验人员根据实验进展优化条件，但需要完整记录所有变更以确保实验数据的可追溯性。

## 数据导出增强设计

### 时间格式处理

基于需求7，系统提供多种时间格式的数据导出：

```csharp
public class DataExportService
{
    public class ExportOptions
    {
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public ExportFormat Format { get; set; }
        public TimeFormat TimeFormat { get; set; }
        public bool IncludeParameterChanges { get; set; }
        public bool IncludeDeviceStatus { get; set; }
    }
    
    public enum TimeFormat
    {
        AbsoluteTimestamp,      // 绝对时间戳
        RelativeSeconds,        // 相对于实验开始的秒数
        Both                    // 两种格式都包含
    }
    
    public async Task<byte[]> ExportDataAsync(Guid experimentId, ExportOptions options)
    {
        var experiment = await GetExperimentAsync(experimentId);
        var dataPoints = await GetDataPointsAsync(experimentId, options.StartTime, options.EndTime);
        
        var exportData = dataPoints.Select(dp => new
        {
            // 根据选项决定时间格式
            Timestamp = options.TimeFormat switch
            {
                TimeFormat.AbsoluteTimestamp => dp.Timestamp.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                TimeFormat.RelativeSeconds => dp.ElapsedSeconds.ToString("F3"),
                TimeFormat.Both => $"{dp.Timestamp:yyyy-MM-dd HH:mm:ss.fff} ({dp.ElapsedSeconds:F3}s)",
                _ => dp.Timestamp.ToString()
            },
            ElapsedSeconds = dp.ElapsedSeconds,
            Voltage = dp.Voltage.ToString("F3"),
            Current = dp.Current.ToString("F2"),
            Temperature = dp.Temperature?.ToString("F2") ?? "",
            FlowRate1 = dp.FlowRate1?.ToString("F2") ?? "",
            FlowRate2 = dp.FlowRate2?.ToString("F2") ?? ""
        });
        
        // 如果包含参数变更记录
        if (options.IncludeParameterChanges)
        {
            var parameterChanges = await GetParameterChangesAsync(experimentId, options.StartTime, options.EndTime);
            // 将参数变更信息合并到导出数据中
        }
        
        return options.Format switch
        {
            ExportFormat.CSV => GenerateCSV(exportData),
            ExportFormat.Excel => GenerateExcel(exportData),
            _ => throw new NotSupportedException($"不支持的导出格式: {options.Format}")
        };
    }
}
```

**设计决策理由**：提供灵活的时间格式选项，满足不同分析需求。相对秒数便于数据分析，绝对时间戳便于事件关联。

## 界面原型设计需求

### 原型开发策略

基于需求22和需求23，系统开发采用原型先行的策略：

#### 原型设计要求

1. **技术实现**：使用单个HTML文件（包含HTML、CSS、JavaScript）
2. **功能覆盖**：包含所有主要功能界面
3. **交互演示**：能够演示完整的用户操作流程
4. **沟通工具**：作为与实验人员确认需求的媒介

#### 原型包含的主要界面

- **实验参数设置界面**：三种实验模式的参数配置
- **实时数据显示界面**：数据图表和数值显示
- **设备控制界面**：设备状态监控和手动控制
- **实验控制界面**：开始、停止、紧急停止按钮
- **数据导出界面**：历史数据查询和导出功能

#### 设计确认文档结构

原型完成后需要撰写系统设计方案文档，包含：

1. **前期沟通汇总**：需求收集和分析过程
2. **界面设计说明**：详细的界面布局和功能说明
3. **实验流程描述**：完整的操作流程和状态转换
4. **技术架构概述**：系统架构和关键技术决策
5. **实施计划**：开发阶段和里程碑安排

**设计决策理由**：原型先行的方法可以在编码前发现和解决界面设计问题，减少后期修改成本，确保最终产品符合用户期望。

## 数据库架构增强

### 参数变更记录表

为支持实时参数调整功能，需要添加参数变更记录表：

```sql
CREATE TABLE ParameterChanges (
    Id BIGINT IDENTITY(1,1) PRIMARY KEY,
    ExperimentId UNIQUEIDENTIFIER NOT NULL,
    ParameterName NVARCHAR(100) NOT NULL,
    OldValue DECIMAL(18,6) NOT NULL,
    NewValue DECIMAL(18,6) NOT NULL,
    ChangeReason NVARCHAR(500) NULL,
    Timestamp DATETIME2 NOT NULL,
    CreatedAt DATETIME2 DEFAULT GETDATE(),
    FOREIGN KEY (ExperimentId) REFERENCES Experiments(Id)
);

CREATE INDEX IX_ParameterChanges_ExperimentId_Timestamp 
ON ParameterChanges(ExperimentId, Timestamp);
```

### 实验状态表

为支持崩溃恢复功能，需要添加实验状态表：

```sql
CREATE TABLE ExperimentStates (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    ExperimentId UNIQUEIDENTIFIER NOT NULL,
    Status INT NOT NULL,
    Configuration NVARCHAR(MAX) NOT NULL,
    CurrentCycle INT DEFAULT 0,
    CurrentProgress DECIMAL(5,2) DEFAULT 0,
    DeviceStates NVARCHAR(MAX) NULL,
    StartTime DATETIME2 NULL,
    DeviceReadyTime DATETIME2 NULL,
    ExperimentStartTime DATETIME2 NULL,
    LastSaveTime DATETIME2 NOT NULL,
    CreatedAt DATETIME2 DEFAULT GETDATE(),
    UpdatedAt DATETIME2 DEFAULT GETDATE(),
    FOREIGN KEY (ExperimentId) REFERENCES Experiments(Id)
);
```

**设计决策理由**：独立的状态表便于快速查询和恢复，避免影响主要数据表的性能。参数变更记录确保实验过程的完整可追溯性。