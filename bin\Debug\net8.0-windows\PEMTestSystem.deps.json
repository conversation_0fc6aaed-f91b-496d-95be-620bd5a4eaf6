{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"PEMTestSystem/1.0.0": {"dependencies": {"LiveCharts.Wpf.Core": "0.9.8", "Microsoft.EntityFrameworkCore.SqlServer": "8.0.0", "Microsoft.EntityFrameworkCore.Tools": "8.0.0", "Microsoft.Extensions.Hosting": "8.0.0", "Microsoft.Extensions.Logging.Console": "8.0.0", "Microsoft.Extensions.Logging.Debug": "8.0.0", "Newtonsoft.Json": "13.0.3", "Serilog": "4.3.0", "Serilog.Extensions.Logging": "9.0.2", "Serilog.Sinks.Console": "6.0.0", "Serilog.Sinks.File": "7.0.0", "System.Configuration.ConfigurationManager": "8.0.0", "System.Data.SqlClient": "4.9.0", "System.IO.Ports": "9.0.7"}, "runtime": {"PEMTestSystem.dll": {}}}, "Azure.Core/1.25.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.Diagnostics.DiagnosticSource": "9.0.0", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/net5.0/Azure.Core.dll": {"assemblyVersion": "********", "fileVersion": "1.2500.22.33004"}}}, "Azure.Identity/1.7.0": {"dependencies": {"Azure.Core": "1.25.0", "Microsoft.Identity.Client": "4.47.2", "Microsoft.Identity.Client.Extensions.Msal": "2.19.3", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "8.0.0", "System.Text.Json": "8.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Azure.Identity.dll": {"assemblyVersion": "1.7.0.0", "fileVersion": "1.700.22.46903"}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "LiveCharts.Core/0.9.8": {"runtime": {"lib/netstandard2.0/LiveCharts.dll": {"assemblyVersion": "0.9.7.0", "fileVersion": "0.9.7.0"}}}, "LiveCharts.Wpf.Core/0.9.8": {"dependencies": {"LiveCharts.Core": "0.9.8"}, "runtime": {"lib/net5.0-windows7.0/LiveCharts.Wpf.dll": {"assemblyVersion": "0.9.7.0", "fileVersion": "0.9.7.0"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.CodeAnalysis.Analyzers/3.3.3": {}, "Microsoft.CodeAnalysis.Common/4.5.0": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.3.3", "System.Collections.Immutable": "6.0.0", "System.Reflection.Metadata": "6.0.1", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encoding.CodePages": "6.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/4.5.0": {"dependencies": {"Microsoft.CodeAnalysis.Common": "4.5.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.5.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp": "4.5.0", "Microsoft.CodeAnalysis.Common": "4.5.0", "Microsoft.CodeAnalysis.Workspaces.Common": "4.5.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/4.5.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Microsoft.CodeAnalysis.Common": "4.5.0", "System.Composition": "6.0.0", "System.IO.Pipelines": "6.0.3", "System.Threading.Channels": "6.0.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "4.500.23.10905"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CSharp/4.5.0": {}, "Microsoft.Data.SqlClient/5.1.1": {"dependencies": {"Azure.Identity": "1.7.0", "Microsoft.Data.SqlClient.SNI.runtime": "5.1.0", "Microsoft.Identity.Client": "4.47.2", "Microsoft.IdentityModel.JsonWebTokens": "6.24.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.24.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "8.0.0", "System.Diagnostics.DiagnosticSource": "9.0.0", "System.Runtime.Caching": "6.0.0", "System.Security.Cryptography.Cng": "5.0.0", "System.Security.Principal.Windows": "5.0.0", "System.Text.Encoding.CodePages": "6.0.0", "System.Text.Encodings.Web": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "runtimeTargets": {"runtimes/unix/lib/net6.0/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}, "runtimes/win/lib/net6.0/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.0": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*******"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*******"}}}, "Microsoft.EntityFrameworkCore/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "8.0.0", "Microsoft.EntityFrameworkCore.Analyzers": "8.0.0", "Microsoft.Extensions.Caching.Memory": "8.0.0", "Microsoft.Extensions.Logging": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.0": {"runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.0": {}, "Microsoft.EntityFrameworkCore.Design/8.0.0": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.CSharp.Workspaces": "4.5.0", "Microsoft.EntityFrameworkCore.Relational": "8.0.0", "Microsoft.Extensions.DependencyModel": "8.0.0", "Mono.TextTemplating": "2.2.1"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Relational/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.SqlServer/8.0.0": {"dependencies": {"Microsoft.Data.SqlClient": "5.1.1", "Microsoft.EntityFrameworkCore.Relational": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.EntityFrameworkCore.Tools/8.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "8.0.0"}}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.CommandLine.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.UserSecrets.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyInjection/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.DependencyModel/8.0.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Diagnostics/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "System.Diagnostics.DiagnosticSource": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Diagnostics.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Hosting/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.Configuration.CommandLine": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.0", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.Logging.Console": "8.0.0", "Microsoft.Extensions.Logging.Debug": "8.0.0", "Microsoft.Extensions.Logging.EventLog": "8.0.0", "Microsoft.Extensions.Logging.EventSource": "8.0.0", "Microsoft.Extensions.Options": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Hosting.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "System.Diagnostics.DiagnosticSource": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Logging.Configuration/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Configuration.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Console/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.Options": "9.0.0", "System.Text.Json": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Console.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.Debug/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Debug.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.EventLog/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "System.Diagnostics.EventLog": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Logging.EventSource/8.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Logging": "9.0.0", "Microsoft.Extensions.Logging.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0", "System.Text.Json": "8.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.EventSource.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Options/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.Extensions.Options": "9.0.0", "Microsoft.Extensions.Primitives": "9.0.0"}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.ConfigurationExtensions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.Primitives/9.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Identity.Client/4.47.2": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.24.0"}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.47.2.0", "fileVersion": "4.47.2.0"}}}, "Microsoft.Identity.Client.Extensions.Msal/2.19.3": {"dependencies": {"Microsoft.Identity.Client": "4.47.2", "System.Security.Cryptography.ProtectedData": "8.0.0"}, "runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.Extensions.Msal.dll": {"assemblyVersion": "2.19.3.0", "fileVersion": "2.19.3.0"}}}, "Microsoft.IdentityModel.Abstractions/6.24.0": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.JsonWebTokens/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "6.24.0", "System.Text.Encoding": "4.3.0", "System.Text.Json": "8.0.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "********", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Logging/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "6.24.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "********", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Protocols/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "6.24.0", "Microsoft.IdentityModel.Tokens": "6.24.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "********", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.24.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.24.0", "System.IdentityModel.Tokens.Jwt": "6.24.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "********", "fileVersion": "6.24.0.31013"}}}, "Microsoft.IdentityModel.Tokens/6.24.0": {"dependencies": {"Microsoft.CSharp": "4.5.0", "Microsoft.IdentityModel.Logging": "6.24.0", "System.Security.Cryptography.Cng": "5.0.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "********", "fileVersion": "6.24.0.31013"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.SqlServer.Server/1.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.SqlServer.Server.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "Mono.TextTemplating/2.2.1": {"dependencies": {"System.CodeDom": "4.4.0"}, "runtime": {"lib/netstandard2.0/Mono.TextTemplating.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.1.1"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.7": {"runtimeTargets": {"runtimes/android-arm/native/libSystem.IO.Ports.Native.so": {"rid": "android-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.7": {"runtimeTargets": {"runtimes/android-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "android-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.7": {"runtimeTargets": {"runtimes/android-x64/native/libSystem.IO.Ports.Native.so": {"rid": "android-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.7": {"runtimeTargets": {"runtimes/android-x86/native/libSystem.IO.Ports.Native.so": {"rid": "android-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.7": {"runtimeTargets": {"runtimes/linux-arm/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.7": {"runtimeTargets": {"runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.7": {"runtimeTargets": {"runtimes/linux-bionic-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-bionic-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.7": {"runtimeTargets": {"runtimes/linux-bionic-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-bionic-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.7": {"runtimeTargets": {"runtimes/linux-musl-arm/native/libSystem.IO.Ports.Native.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.7": {"runtimeTargets": {"runtimes/linux-musl-arm64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.7": {"runtimeTargets": {"runtimes/linux-musl-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.7": {"runtimeTargets": {"runtimes/linux-x64/native/libSystem.IO.Ports.Native.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.7": {"runtimeTargets": {"runtimes/maccatalyst-arm64/native/libSystem.IO.Ports.Native.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.7": {"runtimeTargets": {"runtimes/maccatalyst-x64/native/libSystem.IO.Ports.Native.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.native.System.Data.SqlClient.sni/4.4.0": {"dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.native.System.IO.Ports/9.0.7": {"dependencies": {"runtime.android-arm.runtime.native.System.IO.Ports": "9.0.7", "runtime.android-arm64.runtime.native.System.IO.Ports": "9.0.7", "runtime.android-x64.runtime.native.System.IO.Ports": "9.0.7", "runtime.android-x86.runtime.native.System.IO.Ports": "9.0.7", "runtime.linux-arm.runtime.native.System.IO.Ports": "9.0.7", "runtime.linux-arm64.runtime.native.System.IO.Ports": "9.0.7", "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports": "9.0.7", "runtime.linux-bionic-x64.runtime.native.System.IO.Ports": "9.0.7", "runtime.linux-musl-arm.runtime.native.System.IO.Ports": "9.0.7", "runtime.linux-musl-arm64.runtime.native.System.IO.Ports": "9.0.7", "runtime.linux-musl-x64.runtime.native.System.IO.Ports": "9.0.7", "runtime.linux-x64.runtime.native.System.IO.Ports": "9.0.7", "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports": "9.0.7", "runtime.maccatalyst-x64.runtime.native.System.IO.Ports": "9.0.7", "runtime.osx-arm64.runtime.native.System.IO.Ports": "9.0.7", "runtime.osx-x64.runtime.native.System.IO.Ports": "9.0.7"}}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.7": {"runtimeTargets": {"runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.7": {"runtimeTargets": {"runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-arm64/native/sni.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x64/native/sni.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"runtimeTargets": {"runtimes/win-x86/native/sni.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "4.6.25512.1"}}}, "Serilog/4.3.0": {"runtime": {"lib/net8.0/Serilog.dll": {"assemblyVersion": "4.3.0.0", "fileVersion": "4.3.0.0"}}}, "Serilog.Extensions.Logging/9.0.2": {"dependencies": {"Microsoft.Extensions.Logging": "9.0.0", "Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Extensions.Logging.dll": {"assemblyVersion": "9.0.2.0", "fileVersion": "9.0.2.0"}}}, "Serilog.Sinks.Console/6.0.0": {"dependencies": {"Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/7.0.0": {"dependencies": {"Serilog": "4.3.0"}, "runtime": {"lib/net8.0/Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.CodeDom/4.4.0": {}, "System.Collections.Immutable/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Composition/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Convention": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0", "System.Composition.TypedParts": "6.0.0"}}, "System.Composition.AttributedModel/6.0.0": {"runtime": {"lib/net6.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Convention/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0"}, "runtime": {"lib/net6.0/System.Composition.Convention.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Hosting/6.0.0": {"dependencies": {"System.Composition.Runtime": "6.0.0"}, "runtime": {"lib/net6.0/System.Composition.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.Runtime/6.0.0": {"runtime": {"lib/net6.0/System.Composition.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Composition.TypedParts/6.0.0": {"dependencies": {"System.Composition.AttributedModel": "6.0.0", "System.Composition.Hosting": "6.0.0", "System.Composition.Runtime": "6.0.0"}, "runtime": {"lib/net6.0/System.Composition.TypedParts.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Configuration.ConfigurationManager/8.0.0": {"dependencies": {"System.Diagnostics.EventLog": "8.0.0", "System.Security.Cryptography.ProtectedData": "8.0.0"}}, "System.Data.SqlClient/4.9.0": {"dependencies": {"runtime.native.System.Data.SqlClient.sni": "4.4.0"}, "runtime": {"lib/net8.0/System.Data.SqlClient.dll": {"assemblyVersion": "*******", "fileVersion": "4.900.24.56208"}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/System.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.900.24.56208"}, "runtimes/win/lib/net8.0/System.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "4.900.24.56208"}}}, "System.Diagnostics.DiagnosticSource/9.0.0": {"runtime": {"lib/net8.0/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Diagnostics.EventLog/8.0.0": {}, "System.Formats.Asn1/5.0.0": {}, "System.IdentityModel.Tokens.Jwt/6.24.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "6.24.0", "Microsoft.IdentityModel.Tokens": "6.24.0"}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "********", "fileVersion": "6.24.0.31013"}}}, "System.IO.Pipelines/6.0.3": {"runtime": {"lib/net6.0/System.IO.Pipelines.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.522.21309"}}}, "System.IO.Ports/9.0.7": {"dependencies": {"runtime.native.System.IO.Ports": "9.0.7"}, "runtime": {"lib/net8.0/System.IO.Ports.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/System.IO.Ports.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}, "runtimes/win/lib/net8.0/System.IO.Ports.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.725.31616"}}}, "System.Memory/4.5.4": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "8.0.0", "System.Text.Json": "8.0.0"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.221.20802"}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Reflection.Metadata/6.0.1": {"dependencies": {"System.Collections.Immutable": "6.0.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.Caching/6.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "8.0.0"}, "runtime": {"lib/net6.0/System.Runtime.Caching.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.Cryptography.Cng/5.0.0": {"dependencies": {"System.Formats.Asn1": "5.0.0"}}, "System.Security.Cryptography.ProtectedData/8.0.0": {}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.CodePages/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Text.Encodings.Web/8.0.0": {}, "System.Text.Json/8.0.0": {"dependencies": {"System.Text.Encodings.Web": "8.0.0"}}, "System.Threading.Channels/6.0.0": {}, "System.Threading.Tasks.Extensions/4.5.4": {}}}, "libraries": {"PEMTestSystem/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Azure.Core/1.25.0": {"type": "package", "serviceable": true, "sha512": "sha512-X8Dd4sAggS84KScWIjEbFAdt2U1KDolQopTPoHVubG2y3CM54f9l6asVrP5Uy384NWXjsspPYaJgz5xHc+KvTA==", "path": "azure.core/1.25.0", "hashPath": "azure.core.1.25.0.nupkg.sha512"}, "Azure.Identity/1.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-eHEiCO/8+MfNc9nH5dVew/+FvxdaGrkRL4OMNwIz0W79+wtJyEoeRlXJ3SrXhoy9XR58geBYKmzMR83VO7bcAw==", "path": "azure.identity/1.7.0", "hashPath": "azure.identity.1.7.0.nupkg.sha512"}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "LiveCharts.Core/0.9.8": {"type": "package", "serviceable": true, "sha512": "sha512-USb2TBOeER19X0ar5Zgki1ezjrSXBOmUoz3IqzTeksDm6hoaLFqs3iW9+wjHFuWNQGy9lLcVh2YIhhxVIg+zNQ==", "path": "livecharts.core/0.9.8", "hashPath": "livecharts.core.0.9.8.nupkg.sha512"}, "LiveCharts.Wpf.Core/0.9.8": {"type": "package", "serviceable": true, "sha512": "sha512-jMBdknuG3EOMKA0NaqqwMjhsZpRAzd0UqP0SlwXIBJ+ZoJg5TF9C4rDdKqInEs+RycYmIwmrZWvmfDxsM5MMJw==", "path": "livecharts.wpf.core/0.9.8", "hashPath": "livecharts.wpf.core.0.9.8.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-j/rOZtLMVJjrfLRlAMckJLPW/1rze9MT1yfWqSIbUPGRu1m1P0fuo9PmqapwsmePfGB5PJrudQLvmUOAMF0DqQ==", "path": "microsoft.codeanalysis.analyzers/3.3.3", "hashPath": "microsoft.codeanalysis.analyzers.3.3.3.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-lwAbIZNdnY0SUNoDmZHkVUwLO8UyNnyyh1t/4XsbFxi4Ounb3xszIYZaWhyj5ZjyfcwqwmtMbE7fUTVCqQEIdQ==", "path": "microsoft.codeanalysis.common/4.5.0", "hashPath": "microsoft.codeanalysis.common.4.5.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-cM59oMKAOxvdv76bdmaKPy5hfj+oR+zxikWoueEB7CwTko7mt9sVKZI8Qxlov0C/LuKEG+WQwifepqL3vuTiBQ==", "path": "microsoft.codeanalysis.csharp/4.5.0", "hashPath": "microsoft.codeanalysis.csharp.4.5.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-h74wTpmGOp4yS4hj+EvNzEiPgg/KVs2wmSfTZ81upJZOtPkJsVkgfsgtxxqmAeapjT/vLKfmYV0bS8n5MNVP+g==", "path": "microsoft.codeanalysis.csharp.workspaces/4.5.0", "hashPath": "microsoft.codeanalysis.csharp.workspaces.4.5.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4dDRmGELXG72XZaonnOeORyD/T5RpEu5LGHOUIhnv+MmUWDY/m1kWXGwtcgQ5CJ5ynkFiRnIYzTKXYjUs7rbw==", "path": "microsoft.codeanalysis.workspaces.common/4.5.0", "hashPath": "microsoft.codeanalysis.workspaces.common.4.5.0.nupkg.sha512"}, "Microsoft.CSharp/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-kaj6Wb4qoMuH3HySFJhxwQfe8R/sJsNJnANrvv8WdFPMoNbKY5htfNscv+LHCu5ipz+49m2e+WQXpLXr9XYemQ==", "path": "microsoft.csharp/4.5.0", "hashPath": "microsoft.csharp.4.5.0.nupkg.sha512"}, "Microsoft.Data.SqlClient/5.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-MW5E9HFvCaV069o8b6YpuRDPBux8s96qDnOJ+4N9QNUCs7c5W3KxwQ+ftpAjbMUlImL+c9WR+l+f5hzjkqhu2g==", "path": "microsoft.data.sqlclient/5.1.1", "hashPath": "microsoft.data.sqlclient.5.1.1.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/5.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-jVsElisM5sfBzaaV9kdq2NXZLwIbytetnsOIlJ0cQGgQP4zFNBmkfHBnpwtmKrtBJBEV9+9PVQPVrcCVhDgcIg==", "path": "microsoft.data.sqlclient.sni.runtime/5.1.0", "hashPath": "microsoft.data.sqlclient.sni.runtime.5.1.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SoODat83pGQUpWB9xULdMX6tuKpq/RTXDuJ2WeC1ldUKcKzLkaFJD1n+I0nOLY58odez/e7z8b6zdp235G/kyg==", "path": "microsoft.entityframeworkcore/8.0.0", "hashPath": "microsoft.entityframeworkcore.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VR22s3+zoqlVI7xauFKn1znSIFHO8xuILT+noSwS8bZCKcHz0ydkTDQMuaxSa5WBaQrZmwtTz9rmRvJ7X8mSPQ==", "path": "microsoft.entityframeworkcore.abstractions/8.0.0", "hashPath": "microsoft.entityframeworkcore.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZXxEeLs2zoZ1TA+QoMMcw4f3Tirf8PzgdDax8RoWo0dxI2KmqiEGWYjhm2B/XyWfglc6+mNRyB8rZiQSmxCpeg==", "path": "microsoft.entityframeworkcore.analyzers/8.0.0", "hashPath": "microsoft.entityframeworkcore.analyzers.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-94reKYu63jg4O75UI3LMJHwOSi8tQ6IfubiZhdnSsWcgtmAuF8OyLfjK/MIxuvaQRJZAF6E747FIuxjOtb8/og==", "path": "microsoft.entityframeworkcore.design/8.0.0", "hashPath": "microsoft.entityframeworkcore.design.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fFKkr24cYc7Zw5T6DC4tEyOEPgPbq23BBmym1r9kn4ET9F3HKaetpOeQtV2RryYyUxEeNkJuxgfiZHTisqZc+A==", "path": "microsoft.entityframeworkcore.relational/8.0.0", "hashPath": "microsoft.entityframeworkcore.relational.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GeOmafQn64HyQtYcI/Omv/D/YVHd1zEkWbP3zNQu4oC+usE9K0qOp0R8KgWWFEf8BU4tXuYbok40W0SjfbaK/A==", "path": "microsoft.entityframeworkcore.sqlserver/8.0.0", "hashPath": "microsoft.entityframeworkcore.sqlserver.8.0.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-zRdaXiiB1gEA0b+AJTd2+drh78gkEA4HyZ1vqNZrKq4xwW8WwavSiQsoeb1UsIMZkocLMBbhQYWClkZzuTKEgQ==", "path": "microsoft.entityframeworkcore.tools/8.0.0", "hashPath": "microsoft.entityframeworkcore.tools.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3KuSxeHoNYdxVYfg2IRZCThcrlJ1XJqIXkAWikCsbm5C/bCjv7G0WoKDyuR98Q+T607QT2Zl5GsbGRkENcV2yQ==", "path": "microsoft.extensions.caching.abstractions/8.0.0", "hashPath": "microsoft.extensions.caching.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-7pqivmrZDzo1ADPkRwjy+8jtRKWRCPag9qPI+p7sgu7Q4QreWhcvbiWXsbhP+yY8XSiDvZpu2/LWdBv7PnmOpQ==", "path": "microsoft.extensions.caching.memory/8.0.0", "hashPath": "microsoft.extensions.caching.memory.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "path": "microsoft.extensions.configuration/8.0.0", "hashPath": "microsoft.extensions.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "path": "microsoft.extensions.configuration.binder/8.0.0", "hashPath": "microsoft.extensions.configuration.binder.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NZuZMz3Q8Z780nKX3ifV1fE7lS+6pynDHK71OfU4OZ1ItgvDOhyOC7E6z+JMZrAj63zRpwbdldYFk499t3+1dQ==", "path": "microsoft.extensions.configuration.commandline/8.0.0", "hashPath": "microsoft.extensions.configuration.commandline.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-plvZ0ZIpq+97gdPNNvhwvrEZ92kNml9hd1pe3idMA7svR0PztdzVLkoWLcRFgySYXUJc3kSM3Xw3mNFMo/bxRA==", "path": "microsoft.extensions.configuration.environmentvariables/8.0.0", "hashPath": "microsoft.extensions.configuration.environmentvariables.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "path": "microsoft.extensions.configuration.fileextensions/8.0.0", "hashPath": "microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "path": "microsoft.extensions.configuration.json/8.0.0", "hashPath": "microsoft.extensions.configuration.json.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ihDHu2dJYQird9pl2CbdwuNDfvCZdOS0S7SPlNfhPt0B81UTT+yyZKz2pimFZGUp3AfuBRnqUCxB2SjsZKHVUw==", "path": "microsoft.extensions.configuration.usersecrets/8.0.0", "hashPath": "microsoft.extensions.configuration.usersecrets.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MCPrg7v3QgNMr0vX4vzRXvkNGgLg8vKWX0nKCWUxu2uPyMsaRgiRc1tHBnbTcfJMhMKj2slE/j2M9oGkd25DNw==", "path": "microsoft.extensions.dependencyinjection/9.0.0", "hashPath": "microsoft.extensions.dependencyinjection.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+6f2qv2a3dLwd5w6JanPIPs47CxRbnk+ZocMJUhv9NxP88VlOcJYZs9jY+MYSjxvady08bUZn6qgiNh7DadGgg==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NSmDw3K0ozNDgShSIpsZcbFIzBX4w28nDag+TfaQujkXGazBm+lid5onlWoCBy4VsLxqnnKjEBbGSJVWJMf43g==", "path": "microsoft.extensions.dependencymodel/8.0.0", "hashPath": "microsoft.extensions.dependencymodel.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3PZp/YSkIXrF7QK7PfC1bkyRYwqOHpWFad8Qx+4wkuumAeXo1NHaxpS9LboNA9OvNSAu+QOVlXbMyoY+pHSqcw==", "path": "microsoft.extensions.diagnostics/8.0.0", "hashPath": "microsoft.extensions.diagnostics.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Diagnostics.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "path": "microsoft.extensions.diagnostics.abstractions/8.0.0", "hashPath": "microsoft.extensions.diagnostics.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "hashPath": "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "hashPath": "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "hashPath": "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ItYHpdqVp5/oFLT5QqbopnkKlyFG9EW/9nhM6/yfObeKt6Su0wkBio6AizgRHGNwhJuAtlE5VIjow5JOTrip6w==", "path": "microsoft.extensions.hosting/8.0.0", "hashPath": "microsoft.extensions.hosting.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "path": "microsoft.extensions.hosting.abstractions/8.0.0", "hashPath": "microsoft.extensions.hosting.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-crj<PERSON>yORoug0kK7RSNJBTeSE6VX8IQgLf3nUpTB9m62bPXp/tzbnOsnbe8TXEG0AASNaKZddnpHKw7fET8E++Pg==", "path": "microsoft.extensions.logging/9.0.0", "hashPath": "microsoft.extensions.logging.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-g0UfujELzlLbHoVG8kPKVBaW470Ewi+jnptGS9KUi6jcb+k2StujtK3m26DFSGGwQ/+bVgZfsWqNzlP6YOejvw==", "path": "microsoft.extensions.logging.abstractions/9.0.0", "hashPath": "microsoft.extensions.logging.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ixXXV0G/12g6MXK65TLngYN9V5hQQRuV+fZi882WIoVJT7h5JvoYoxTEwCgdqwLjSneqh1O+66gM8sMr9z/rsQ==", "path": "microsoft.extensions.logging.configuration/8.0.0", "hashPath": "microsoft.extensions.logging.configuration.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-e+48o7DztoYog+PY430lPxrM4mm3PbA6qucvQtUDDwVo4MO+ejMw7YGc/o2rnxbxj4isPxdfKFzTxvXMwAz83A==", "path": "microsoft.extensions.logging.console/8.0.0", "hashPath": "microsoft.extensions.logging.console.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dt0x21qBdudHLW/bjMJpkixv858RRr8eSomgVbU8qljOyfrfDGi1JQvpF9w8S7ziRPtRKisuWaOwFxJM82GxeA==", "path": "microsoft.extensions.logging.debug/8.0.0", "hashPath": "microsoft.extensions.logging.debug.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventLog/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3X9D3sl7EmOu7vQp5MJrmIJBl5XSdOhZPYXUeFfYa6Nnm9+tok8x3t3IVPLhm7UJtPOU61ohFchw8rNm9tIYOQ==", "path": "microsoft.extensions.logging.eventlog/8.0.0", "hashPath": "microsoft.extensions.logging.eventlog.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.EventSource/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-oKcPMrw+luz2DUAKhwFXrmFikZWnyc8l2RKoQwqU3KIZZjcfoJE0zRHAnqATfhRZhtcbjl/QkiY2Xjxp0xu+6w==", "path": "microsoft.extensions.logging.eventsource/8.0.0", "hashPath": "microsoft.extensions.logging.eventsource.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-y2146b3jrPI3Q0lokKXdKLpmXqakYbDIPDV6r3M8SqvSf45WwOTzkyfDpxnZXJsJQEpAsAqjUq5Pu8RCJMjubg==", "path": "microsoft.extensions.options/9.0.0", "hashPath": "microsoft.extensions.options.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "path": "microsoft.extensions.options.configurationextensions/8.0.0", "hashPath": "microsoft.extensions.options.configurationextensions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N3qEBzmLMYiASUlKxxFIISP4AiwuPTHF5uCh+2CWSwwzAJiIYx0kBJsS30cp1nvhSySFAVi30jecD307jV+8Kg==", "path": "microsoft.extensions.primitives/9.0.0", "hashPath": "microsoft.extensions.primitives.9.0.0.nupkg.sha512"}, "Microsoft.Identity.Client/4.47.2": {"type": "package", "serviceable": true, "sha512": "sha512-SPgesZRbXoDxg8Vv7k5Ou0ee7uupVw0E8ZCc4GKw25HANRLz1d5OSr0fvTVQRnEswo5Obk8qD4LOapYB+n5kzQ==", "path": "microsoft.identity.client/4.47.2", "hashPath": "microsoft.identity.client.4.47.2.nupkg.sha512"}, "Microsoft.Identity.Client.Extensions.Msal/2.19.3": {"type": "package", "serviceable": true, "sha512": "sha512-zVVZjn8aW7W79rC1crioDgdOwaFTQorsSO6RgVlDDjc7MvbEGz071wSNrjVhzR0CdQn6Sefx7Abf1o7vasmrLg==", "path": "microsoft.identity.client.extensions.msal/2.19.3", "hashPath": "microsoft.identity.client.extensions.msal.2.19.3.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-X6aBK56Ot15qKyG7X37KsPnrwah+Ka55NJWPppWVTDi8xWq7CJgeNw2XyaeHgE1o/mW4THwoabZkBbeG2TPBiw==", "path": "microsoft.identitymodel.abstractions/6.24.0", "hashPath": "microsoft.identitymodel.abstractions.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-XDWrkThcxfuWp79AvAtg5f+uRS1BxkIbJnsG/e8VPzOWkYYuDg33emLjp5EWcwXYYIDsHnVZD/00kM/PYFQc/g==", "path": "microsoft.identitymodel.jsonwebtokens/6.24.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-qLYWDOowM/zghmYKXw1yfYKlHOdS41i8t4hVXr9bSI90zHqhyhQh9GwVy8pENzs5wHeytU23DymluC9NtgYv7w==", "path": "microsoft.identitymodel.logging/6.24.0", "hashPath": "microsoft.identitymodel.logging.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-+NzKCkvsQ8X1r/Ff74V7CFr9OsdMRaB6DsV+qpH7NNLdYJ8O4qHbmTnNEsjFcDmk/gVNDwhoL2gN5pkPVq0lwQ==", "path": "microsoft.identitymodel.protocols/6.24.0", "hashPath": "microsoft.identitymodel.protocols.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-a/2RRrc8C9qaw8qdD9hv1ES9YKFgxaqr/SnwMSLbwQZJSUQDd4qx1K4EYgWaQWs73R+VXLyKSxN0f/uE9CsBiQ==", "path": "microsoft.identitymodel.protocols.openidconnect/6.24.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.24.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZPqHi86UYuqJXJ7bLnlEctHKkPKT4lGUFbotoCNiXNCSL02emYlcxzGYsRGWWmbFEcYDMi2dcTLLYNzHqWOTsw==", "path": "microsoft.identitymodel.tokens/6.24.0", "hashPath": "microsoft.identitymodel.tokens.6.24.0.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.SqlServer.Server/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug==", "path": "microsoft.sqlserver.server/1.0.0", "hashPath": "microsoft.sqlserver.server.1.0.0.nupkg.sha512"}, "Mono.TextTemplating/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-KZYeKBET/2Z0gY1WlTAK7+RHTl7GSbtvTLDXEZZojUdAPqpQNDL6tHv7VUpqfX5VEOh+uRGKaZXkuD253nEOBQ==", "path": "mono.texttemplating/2.2.1", "hashPath": "mono.texttemplating.2.2.1.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-xtOeUEMiUqYbYCbH6LuU7jH0KD0fAdMPjC2OmcXn9bTmZYiBazZBiVKQlekCmeXrBf39siRbx8TNnVI8U9RWVA==", "path": "runtime.android-arm.runtime.native.system.io.ports/9.0.7", "hashPath": "runtime.android-arm.runtime.native.system.io.ports.9.0.7.nupkg.sha512"}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-F3SHKwakrr9YenXhjk73bn5W8IQGXpBKTl3qmHUwdmkEKtc8J/30D6OA0/0M1oG3w/YvdgBiWIuy4fnId/98uQ==", "path": "runtime.android-arm64.runtime.native.system.io.ports/9.0.7", "hashPath": "runtime.android-arm64.runtime.native.system.io.ports.9.0.7.nupkg.sha512"}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-uRt8zM34o9NRn9PT8/IxPf4eR4wMNeSLoCTtixekALpwg6GKdGn2JxuWoyzRYK513vUgEBl9TXVF0lPEgJnASg==", "path": "runtime.android-x64.runtime.native.system.io.ports/9.0.7", "hashPath": "runtime.android-x64.runtime.native.system.io.ports.9.0.7.nupkg.sha512"}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-az6Dxw9DZ2I4ypY0cgML6XXKvi3yK1Tevlsv4AdeV0qaD/7hpepuJr2XpPleWF744QHlVIhMlLX1fMcQM0Zr4w==", "path": "runtime.android-x86.runtime.native.system.io.ports/9.0.7", "hashPath": "runtime.android-x86.runtime.native.system.io.ports.9.0.7.nupkg.sha512"}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-uGkizTYesvArtyzD50BlIY4dlbs36OT0KNsPuwG9wEQvpz4WGiKbUD494Y6nfGJoIFkW0hdqOBljqE234tk26A==", "path": "runtime.linux-arm.runtime.native.system.io.ports/9.0.7", "hashPath": "runtime.linux-arm.runtime.native.system.io.ports.9.0.7.nupkg.sha512"}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-HTyktEebT2q6Et9XSG5hTWqt69n7hI+0UBHBOPeSlMZ6cMSR/MW5bO1yFqaR/+U6nM08zRCpaBOGOw2GPTq1eg==", "path": "runtime.linux-arm64.runtime.native.system.io.ports/9.0.7", "hashPath": "runtime.linux-arm64.runtime.native.system.io.ports.9.0.7.nupkg.sha512"}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-N/aKWbFSvESYOqj5IYfzN9GcRXlTsaeJ899XCwiLGoMZIfLZTF1ejaR6Ikq3VnGdv4vilTSlvsDcxvskXwfKWA==", "path": "runtime.linux-bionic-arm64.runtime.native.system.io.ports/9.0.7", "hashPath": "runtime.linux-bionic-arm64.runtime.native.system.io.ports.9.0.7.nupkg.sha512"}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-OyEZXmsoXPGu/kC/JFlVVZiVdVQmFxYwVJPR8CjX3SZTru2cTfDmX7v1d+kbuSrgUjrvLjk77PwbFL6Iz4KkPA==", "path": "runtime.linux-bionic-x64.runtime.native.system.io.ports/9.0.7", "hashPath": "runtime.linux-bionic-x64.runtime.native.system.io.ports.9.0.7.nupkg.sha512"}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-AC42my/QpwBBxRGKVqed1k0Kn1iZ5ZGNN/R6VLByKDHaDlUO7W6VUimle3JN9imC5R8aq9or6VpqFu9mCss0Ew==", "path": "runtime.linux-musl-arm.runtime.native.system.io.ports/9.0.7", "hashPath": "runtime.linux-musl-arm.runtime.native.system.io.ports.9.0.7.nupkg.sha512"}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-xQhCGVeC555KtzeyQnPzWbifovO3mGLiGGUZppluvqAGvygAhiXqrigSMgu0xTowE2u5WwFCIj4Trco2DU9uxg==", "path": "runtime.linux-musl-arm64.runtime.native.system.io.ports/9.0.7", "hashPath": "runtime.linux-musl-arm64.runtime.native.system.io.ports.9.0.7.nupkg.sha512"}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-NQElKMyNNzssg+JFQ1LjPuRng5uoVXbt2aftpUaztDxyBhCBRm/9mZjbWvXS5I5E9dhfNUdFWU40dxpo5bITMw==", "path": "runtime.linux-musl-x64.runtime.native.system.io.ports/9.0.7", "hashPath": "runtime.linux-musl-x64.runtime.native.system.io.ports.9.0.7.nupkg.sha512"}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-wO5758mD+mKBI2V8U/FdfbmcSEW7lLaRohozF72i8x4Ly4o2tmFzYTcjocfJJpTxS8DnK6X9qxP9pQakTwZk7w==", "path": "runtime.linux-x64.runtime.native.system.io.ports/9.0.7", "hashPath": "runtime.linux-x64.runtime.native.system.io.ports.9.0.7.nupkg.sha512"}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-8tpVCB289sH5/w/lrztQaVWi6s6V0Ptrpmm6YJR670YswgUPauxeEbN4wiUp7lxC00bTTJq6tRfBntQ1ryyALw==", "path": "runtime.maccatalyst-arm64.runtime.native.system.io.ports/9.0.7", "hashPath": "runtime.maccatalyst-arm64.runtime.native.system.io.ports.9.0.7.nupkg.sha512"}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-qfdGPrRoZcKLSXNO1vGIWJ0ovTh23Zy2cf0MfnNpQK58Dp27eALuVk0p5kpB4X/bf7I9KO+XorezUSFQZ6MHdw==", "path": "runtime.maccatalyst-x64.runtime.native.system.io.ports/9.0.7", "hashPath": "runtime.maccatalyst-x64.runtime.native.system.io.ports.9.0.7.nupkg.sha512"}, "runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-A8v6PGmk+UGbfWo5Ixup0lPM4swuSwOiayJExZwKIOjTlFFQIsu3QnDXECosBEyrWSPryxBVrdqtJyhK3BaupQ==", "path": "runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.native.System.IO.Ports/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-4IJb6UpOGat7IqKl2QAahMYMc1rMWRf9BWeiNDZBsyNViFOtSftgSjUKzFLtnSlXrbc7qqPCh1M9wOE+WNgcOQ==", "path": "runtime.native.system.io.ports/9.0.7", "hashPath": "runtime.native.system.io.ports.9.0.7.nupkg.sha512"}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-pOZCQaB07EYIWxT5zMgoQr0QLbokVWmItEIOoCqbhY7m8ozV21Jxy5u6ZEL8gMO7QWD+zGbRZQYEQpCNAd+J4g==", "path": "runtime.osx-arm64.runtime.native.system.io.ports/9.0.7", "hashPath": "runtime.osx-arm64.runtime.native.system.io.ports.9.0.7.nupkg.sha512"}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-aZT8Go/6NcnKce+xwMbrAxNc9rA6K3SKXPiK5A+A/wiUrlZK6CQwYueKDmU3zdm/oSrUfHSzk0YuCBknRRp5wA==", "path": "runtime.osx-x64.runtime.native.system.io.ports/9.0.7", "hashPath": "runtime.osx-x64.runtime.native.system.io.ports.9.0.7.nupkg.sha512"}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg==", "path": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-arm64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ==", "path": "runtime.win-x64.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x64.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA==", "path": "runtime.win-x86.runtime.native.system.data.sqlclient.sni/4.4.0", "hashPath": "runtime.win-x86.runtime.native.system.data.sqlclient.sni.4.4.0.nupkg.sha512"}, "Serilog/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+cDryFR0GRhsGOnZSKwaDzRRl4MupvJ42FhCE4zhQRVanX0Jpg6WuCBk59OVhVDPmab1bB+nRykAnykYELA9qQ==", "path": "serilog/4.3.0", "hashPath": "serilog.4.3.0.nupkg.sha512"}, "Serilog.Extensions.Logging/9.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-p8kk2McN6LxuQfLyCoOkL7+nJIhVKnV1WFUxAaGTQTQk0wySbgmCHe98j+xSQvIbYHtzKXROOE2G2R0TLwBfig==", "path": "serilog.extensions.logging/9.0.2", "hashPath": "serilog.extensions.logging.9.0.2.nupkg.sha512"}, "Serilog.Sinks.Console/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fQGWqVMClCP2yEyTXPIinSr5c+CBGUvBybPxjAGcf7ctDhadFhrQw03Mv8rJ07/wR5PDfFjewf2LimvXCDzpbA==", "path": "serilog.sinks.console/6.0.0", "hashPath": "serilog.sinks.console.6.0.0.nupkg.sha512"}, "Serilog.Sinks.File/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fKL7mXv7qaiNBUC71ssvn/dU0k9t0o45+qm2XgKAlSt19xF+ijjxyA3R6HmCgfKEKwfcfkwWjayuQtRueZFkYw==", "path": "serilog.sinks.file/7.0.0", "hashPath": "serilog.sinks.file.7.0.0.nupkg.sha512"}, "System.CodeDom/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-2sCCb7doXEwtYAbqzbF/8UAeDRMNmPaQbU2q50Psg1J9KzumyVVCgKQY8s53WIPTufNT0DpSe9QRvVjOzfDWBA==", "path": "system.codedom/4.4.0", "hashPath": "system.codedom.4.4.0.nupkg.sha512"}, "System.Collections.Immutable/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-l4zZJ1WU2hqpQQHXz1rvC3etVZN+2DLmQMO79FhOTZHMn8tDRr+WU287sbomD0BETlmKDn0ygUgVy9k5xkkJdA==", "path": "system.collections.immutable/6.0.0", "hashPath": "system.collections.immutable.6.0.0.nupkg.sha512"}, "System.Composition/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-d7wMuKQtfsxUa7S13tITC8n1cQzewuhD5iDjZtK2prwFfKVzdYtgrTHgjaV03Zq7feGQ5gkP85tJJntXwInsJA==", "path": "system.composition/6.0.0", "hashPath": "system.composition.6.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WK1nSDLByK/4VoC7fkNiFuTVEiperuCN/Hyn+VN30R+W2ijO1d0Z2Qm0ScEl9xkSn1G2MyapJi8xpf4R8WRa/w==", "path": "system.composition.attributedmodel/6.0.0", "hashPath": "system.composition.attributedmodel.6.0.0.nupkg.sha512"}, "System.Composition.Convention/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-XYi4lPRdu5bM4JVJ3/UIHAiG6V6lWWUlkhB9ab4IOq0FrRsp0F4wTyV4Dj+Ds+efoXJ3qbLqlvaUozDO7OLeXA==", "path": "system.composition.convention/6.0.0", "hashPath": "system.composition.convention.6.0.0.nupkg.sha512"}, "System.Composition.Hosting/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-w/wXjj7kvxuHPLdzZ0PAUt++qJl03t7lENmb2Oev0n3zbxyNULbWBlnd5J5WUMMv15kg5o+/TCZFb6lSwfaUUQ==", "path": "system.composition.hosting/6.0.0", "hashPath": "system.composition.hosting.6.0.0.nupkg.sha512"}, "System.Composition.Runtime/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qkRH/YBaMPTnzxrS5RDk1juvqed4A6HOD/CwRcDGyPpYps1J27waBddiiq1y93jk2ZZ9wuA/kynM+NO0kb3PKg==", "path": "system.composition.runtime/6.0.0", "hashPath": "system.composition.runtime.6.0.0.nupkg.sha512"}, "System.Composition.TypedParts/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iUR1eHrL8Cwd82neQCJ00MpwNIBs4NZgXzrPqx8NJf/k4+mwBO0XCRmHYJT4OLSwDDqh5nBLJWkz5cROnrGhRA==", "path": "system.composition.typedparts/6.0.0", "hashPath": "system.composition.typedparts.6.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-JlYi9XVvIREURRUlGMr1F6vOFLk7YSY4p1vHo4kX3tQ0AGrjqlRWHDi66ImHhy6qwXBG3BJ6Y1QlYQ+Qz6Xgww==", "path": "system.configuration.configurationmanager/8.0.0", "hashPath": "system.configuration.configurationmanager.8.0.0.nupkg.sha512"}, "System.Data.SqlClient/4.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-j4KJO+vC62NyUtNHz854njEqXbT8OmAa5jb1nrGfYWBOcggyYUQE0w/snXeaCjdvkSKWuUD+hfvlbN8pTrJTXg==", "path": "system.data.sqlclient/4.9.0", "hashPath": "system.data.sqlclient.4.9.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ddppcFpnbohLWdYKr/ZeLZHmmI+DXFgZ3Snq+/E7SwcdW4UnvxmaugkwGywvGVWkHPGCSZjCP+MLzu23AL5SDw==", "path": "system.diagnostics.diagnosticsource/9.0.0", "hashPath": "system.diagnostics.diagnosticsource.9.0.0.nupkg.sha512"}, "System.Diagnostics.EventLog/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-fdYxcRjQqTTacKId/2IECojlDSFvp7LP5N78+0z/xH7v/Tuw5ZAxu23Y6PTCRinqyu2ePx+Gn1098NC6jM6d+A==", "path": "system.diagnostics.eventlog/8.0.0", "hashPath": "system.diagnostics.eventlog.8.0.0.nupkg.sha512"}, "System.Formats.Asn1/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MTvUIktmemNB+El0Fgw9egyqT9AYSIk6DTJeoDSpc3GIHxHCMo8COqkWT1mptX5tZ1SlQ6HJZ0OsSvMth1c12w==", "path": "system.formats.asn1/5.0.0", "hashPath": "system.formats.asn1.5.0.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/6.24.0": {"type": "package", "serviceable": true, "sha512": "sha512-Qibsj9MPWq8S/C0FgvmsLfIlHLE7ay0MJIaAmK94ivN3VyDdglqReed5qMvdQhSL0BzK6v0Z1wB/sD88zVu6Jw==", "path": "system.identitymodel.tokens.jwt/6.24.0", "hashPath": "system.identitymodel.tokens.jwt.6.24.0.nupkg.sha512"}, "System.IO.Pipelines/6.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-ryTgF+iFkpGZY1vRQhfCzX0xTdlV3pyaTTqRu2ETbEv+HlV7O6y7hyQURnghNIXvctl5DuZ//Dpks6HdL/Txgw==", "path": "system.io.pipelines/6.0.3", "hashPath": "system.io.pipelines.6.0.3.nupkg.sha512"}, "System.IO.Ports/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-IqnvqsPwKmMBffVpm0PtyxHDQ13b7iN7V4BL/uoQiRhsYnBMoWnX5lpEQNJFzP9SsyqqnkWbsM+z/MkQ9EhUxA==", "path": "system.io.ports/9.0.7", "hashPath": "system.io.ports.9.0.7.nupkg.sha512"}, "System.Memory/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw==", "path": "system.memory/4.5.4", "hashPath": "system.memory.4.5.4.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Reflection.Metadata/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-III/lNMSn0ZRBuM9m5Cgbiho5j81u0FAEagFX5ta2DKbljZ3T0IpD8j+BIiHQPeKqJppWS9bGEp6JnKnWKze0g==", "path": "system.reflection.metadata/6.0.1", "hashPath": "system.reflection.metadata.6.0.1.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.Caching/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-E0e03kUp5X2k+UAoVl6efmI7uU7JRBWi5EIdlQ7cr0NpBGjHG4fWII35PgsBY9T4fJQ8E4QPsL0rKksU9gcL5A==", "path": "system.runtime.caching/6.0.0", "hashPath": "system.runtime.caching.6.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-jIMXsKn94T9JY7PvPq/tMfqa6GAaHpElRDpmG+SuL+D3+sTw2M8VhnibKnN8Tq+4JqbPJ/f+BwtLeDMEnzAvRg==", "path": "system.security.cryptography.cng/5.0.0", "hashPath": "system.security.cryptography.cng.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg==", "path": "system.security.cryptography.protecteddata/8.0.0", "hashPath": "system.security.cryptography.protecteddata.8.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZFCILZuOvtKPauZ/j/swhvw68ZRi9ATCfvGbk1QfydmcXBkIWecWKn/250UH7rahZ5OoDBaiAudJtPvLwzw85A==", "path": "system.text.encoding.codepages/6.0.0", "hashPath": "system.text.encoding.codepages.6.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "path": "system.text.encodings.web/8.0.0", "hashPath": "system.text.encodings.web.8.0.0.nupkg.sha512"}, "System.Text.Json/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>drZ<PERSON>2WjkiEG6ajEFRABTRCi/wuXQPxeV6g8xvUJqdxMvvuCCEk86zPla8UiIQJz3durtUEbNyY/3lIhS0yZvQ==", "path": "system.text.json/8.0.0", "hashPath": "system.text.json.8.0.0.nupkg.sha512"}, "System.Threading.Channels/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-TY8/9+tI0mNaUMgntOxxaq2ndTkdXqLSxvPmas7XEqOlv9lQtB7wLjYGd756lOaO7Dvb5r/WXhluM+0Xe87v5Q==", "path": "system.threading.channels/6.0.0", "hashPath": "system.threading.channels.6.0.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}}}