using System;
using System.Threading.Tasks;

namespace PEMTestSystem.Services.Devices
{
    /// <summary>
    /// 串口管理器使用示例
    /// 展示如何使用串口管理器进行设备注册和从站地址冲突检查
    /// </summary>
    public class SerialPortManagerUsageExample
    {
        private readonly SerialPortManager _serialPortManager;

        public SerialPortManagerUsageExample(SerialPortManager serialPortManager)
        {
            _serialPortManager = serialPortManager ?? throw new ArgumentNullException(nameof(serialPortManager));
        }

        /// <summary>
        /// 演示设备注册和从站地址冲突检查
        /// </summary>
        public async Task DemonstrateDeviceRegistrationAsync()
        {
            try
            {
                App.AlarmService.Info("示例", "开始演示设备注册和从站地址冲突检查");

                // 注册第一个设备 - 温控器
                var success1 = await _serialPortManager.RegisterDeviceAsync(
                    "TempController_Main", 
                    "COM4", 
                    9600, 
                    3);

                if (success1)
                {
                    App.AlarmService.Info("示例", "温控器注册成功 - 端口: COM4, 波特率: 9600, 从站地址: 3");
                }

                // 注册第二个设备 - 流量泵1
                var success2 = await _serialPortManager.RegisterDeviceAsync(
                    "FlowPump_1", 
                    "COM4", 
                    9600, 
                    1);

                if (success2)
                {
                    App.AlarmService.Info("示例", "流量泵1注册成功 - 端口: COM4, 波特率: 9600, 从站地址: 1");
                }

                // 尝试注册第三个设备，使用相同的从站地址（应该失败）
                var success3 = await _serialPortManager.RegisterDeviceAsync(
                    "FlowPump_2", 
                    "COM4", 
                    9600, 
                    3); // 与温控器相同的从站地址

                if (!success3)
                {
                    App.AlarmService.Warning("示例", "流量泵2注册失败 - 从站地址冲突（地址3已被温控器使用）");
                }

                // 使用不同的从站地址注册第三个设备
                var success4 = await _serialPortManager.RegisterDeviceAsync(
                    "FlowPump_2", 
                    "COM4", 
                    9600, 
                    2);

                if (success4)
                {
                    App.AlarmService.Info("示例", "流量泵2注册成功 - 端口: COM4, 波特率: 9600, 从站地址: 2");
                }

                // 尝试注册设备到相同端口但不同波特率（应该失败）
                var success5 = await _serialPortManager.RegisterDeviceAsync(
                    "AnotherDevice", 
                    "COM4", 
                    19200, // 不同的波特率
                    4);

                if (!success5)
                {
                    App.AlarmService.Warning("示例", "另一个设备注册失败 - 波特率不匹配（端口COM4已使用9600波特率）");
                }

                // 显示端口状态
                await DisplayPortStatusAsync();

                // 演示从站地址冲突检查
                DemonstrateSlaveAddressConflictCheck();

            }
            catch (Exception ex)
            {
                App.AlarmService.Error("示例", "演示过程中发生错误", ex);
            }
        }

        /// <summary>
        /// 显示所有端口状态
        /// </summary>
        private async Task DisplayPortStatusAsync()
        {
            App.AlarmService.Info("示例", "=== 端口状态信息 ===");

            var portStatuses = _serialPortManager.GetAllPortStatus();
            
            foreach (var kvp in portStatuses)
            {
                var status = kvp.Value;
                App.AlarmService.Info("示例", 
                    $"端口: {status.PortName}, " +
                    $"波特率: {status.BaudRate}, " +
                    $"设备数量: {status.DeviceCount}, " +
                    $"状态: {(status.IsOpen ? "打开" : "关闭")}");

                // 显示设备和从站地址映射
                if (status.DeviceSlaveAddresses.Count > 0)
                {
                    App.AlarmService.Info("示例", "设备从站地址映射:");
                    foreach (var deviceAddr in status.DeviceSlaveAddresses)
                    {
                        App.AlarmService.Info("示例", $"  - 设备: {deviceAddr.Key}, 从站地址: {deviceAddr.Value}");
                    }
                }
            }
        }

        /// <summary>
        /// 演示从站地址冲突检查
        /// </summary>
        private void DemonstrateSlaveAddressConflictCheck()
        {
            App.AlarmService.Info("示例", "=== 从站地址冲突检查演示 ===");

            // 检查地址3是否在COM4上冲突
            bool conflict1 = _serialPortManager.IsSlaveAddressConflict("COM4", 3);
            App.AlarmService.Info("示例", $"COM4端口上地址3是否冲突: {(conflict1 ? "是" : "否")}");

            // 检查地址5是否在COM4上冲突
            bool conflict2 = _serialPortManager.IsSlaveAddressConflict("COM4", 5);
            App.AlarmService.Info("示例", $"COM4端口上地址5是否冲突: {(conflict2 ? "是" : "否")}");

            // 检查地址3是否在COM4上冲突（排除温控器）
            bool conflict3 = _serialPortManager.IsSlaveAddressConflict("COM4", 3, "TempController_Main");
            App.AlarmService.Info("示例", $"COM4端口上地址3是否冲突（排除温控器）: {(conflict3 ? "是" : "否")}");

            // 获取COM4端口的设备从站地址映射
            var deviceAddresses = _serialPortManager.GetPortDeviceSlaveAddresses("COM4");
            if (deviceAddresses != null)
            {
                App.AlarmService.Info("示例", "COM4端口设备从站地址映射:");
                foreach (var kvp in deviceAddresses)
                {
                    App.AlarmService.Info("示例", $"  - {kvp.Key}: 地址 {kvp.Value}");
                }
            }
        }

        /// <summary>
        /// 清理演示数据
        /// </summary>
        public async Task CleanupDemoAsync()
        {
            try
            {
                App.AlarmService.Info("示例", "开始清理演示数据");

                // 注销所有演示设备
                await _serialPortManager.UnregisterDeviceAsync("TempController_Main", "COM4");
                await _serialPortManager.UnregisterDeviceAsync("FlowPump_1", "COM4");
                await _serialPortManager.UnregisterDeviceAsync("FlowPump_2", "COM4");

                App.AlarmService.Info("示例", "演示数据清理完成");
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("示例", "清理演示数据时发生错误", ex);
            }
        }
    }
}
