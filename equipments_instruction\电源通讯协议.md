# 我要与电源设备通讯，现在知道了 电源设备的如图中的通讯接口设置方式，还知道这个电源使用如pdf中的协议。是否就可以去设置这个电源设备的输出电压、电流，并读取实时的电流、电压、功率数值。


我们可以把与电源的通讯这个过程分解成两个部分，而您正好拥有这两部分的答案：
“如何连接？” (物理和数据链路层)
您的第一张图片（USB 通讯接口设置.jpg）完美地解答了这个问题。
方式: 您将使用 VCP (虚拟串口通信)。这意味着当您通过USB线将电源连接到电脑后，电脑会把它识别成一个标准的COM端口（例如 COM3, COM4等）。
参数: 您已经知道建立通信所需要的所有参数：
波特率 (Baud Rate): 9600
数据位 (Data Bits): 8
奇偶校验 (Parity): N (None / 无校验)
停止位 (Stop Bits): 1
“连接上之后，说什么？” (应用层)
您提供的 IT-M3900D Series Programming Guide.pdf 就是这个问题的“字典”和“语法书”。
协议: 该电源使用的是 SCPI (Standard Commands for Programmable Instruments) 协议。这是一种标准化的、基于文本的命令语言。
命令: 您只需要从这份PDF中找到具体的命令字符串，发送给电源，它就会执行相应的操作。
具体操作步骤和命令示例
基于您提供的PDF，以下是实现您目标的完整步骤和具体命令：
第一步：建立通信
物理连接: 使用USB线将电源连接到您的电脑。
驱动安装: 确保您的电脑已经正确安装了该电源的USB转串口（VCP）驱动。通常，这会让您的操作系统在“设备管理器”中创建一个新的COM端口。
软件配置: 在您的C#程序中，使用 System.IO.Ports.SerialPort 类，并按照以下参数配置来打开这个COM端口：
PortName: "COMx" (x是您在设备管理器中看到的端口号)
BaudRate: 9600
DataBits: 8
Parity: Parity.None
StopBits: StopBits.One
第二步：发送SCPI命令 (写入)
打开串口后，您就可以通过它发送命令字符串了。关键点：每一个SCPI命令都必须以一个换行符 (\n) 结尾，以表示命令结束。
设置输出电压 (参考PDF第57页 [SOURce:]VOLTage...):
命令: VOLT <电压值>
示例: 要将电压设置为5.5伏，您需要发送的字符串是："VOLT 5.5\n"
设置输出电流 (参考PDF第38页 [SOURce:]CURRent...):
命令: CURR <电流值>
示例: 要将电流设置为2.15安培，您需要发送的字符串是："CURR 2.15\n"
打开/关闭电源输出 (参考PDF第93页 OUTPut[:STATe]...):
命令: OUTP ON 或 OUTP OFF
示例: 要打开输出，发送："OUTP ON\n"。要关闭输出，发送："OUTP OFF\n"。
第三步：发送查询命令并读取返回数据 (读取)
要读取实时数值，您需要发送以问号 (?) 结尾的查询命令。发送后，电源会通过串口返回一个字符串，您需要从串口缓冲区中读取它。
读取实时电压 (参考PDF第204页 MEASure[:SCALar]:VOLTage[:DC]?):
命令: MEAS:VOLT?
操作:
发送字符串: "MEAS:VOLT?\n"
从串口读取返回的字符串（例如 "1.853...")。
读取实时电流 (参考PDF第200页 MEASure[:SCALar]:CURRent[:DC]?):
命令: MEAS:CURR?
操作:
发送字符串: "MEAS:CURR?\n"
从串口读取返回的字符串（例如 "20.15...")。
读取实时功率 (参考PDF第207页 MEASure[:SCALar]:POWer[:DC]?):
命令: MEAS:POW?
操作:
发送字符串: "MEAS:POW?\n"
从串口读取返回的字符串。
总结
结论是肯定的。 您拥有的信息非常完整。您只需按照上述步骤，在您的C#程序中实现串口通信，并使用PDF手册中对应的SCPI命令，就可以完全控制您的ITECH电源，并实时读取其各项数值。

===

以下是 协议 中的相关内容：

===


# ITECH IT-M3900D SCPI 编程手册精选

## 第1章 SCPI 简介 (PDF: 1-11)

### 1.1 概述 (Overview)
SCPI (Standard Commands for Programmable Instruments) 是一种标准化的可编程仪器命令语言，它定义了总线控制器和仪器之间的通信方法。它基于ASCII文本，主要用于测试和测量仪器。SCPI命令基于一种称为树状系统的分层结构。

### 1.2 SCPI 命令类型 (Command Type)
SCPI 有两种类型的命令：**通用命令 (common)** 和 **子系统命令 (subsystem)**。

*   **通用命令**:
    *   通常与具体仪器操作无关，用于控制仪器的整体功能，如复位、状态查询和同步。
    *   所有通用命令都由一个星号 `*` 后跟一个三字母的助记符组成。
    *   示例: `*RST`, `*IDN?`, `*SRE 8`

*   **子系统命令**:
    *   执行特定的仪器功能。
    *   它们被组织成一个倒置的树状结构。

#### 多条命令组合
可以在一条消息中组合多条SCPI命令，使用分号 `;` 分隔。
*   示例: `CURR:LEV 3;PROT:STAT OFF`
    *   解析为: `CURRent:LEVel 3` 和 `CURRent:PROTection:STATe OFF`。

#### 在子系统之间移动
要组合不同子系统的命令，可以在命令前使用冒号 `:` 来重置命令路径到根节点。
*   示例: `PROTection:CLEAr;:STATus:OPERation:CONDition?`

#### 大小写敏感性
SCPI命令不区分大小写。`*RST` 和 `*rst` 效果相同。

#### 长格式和短格式
SCPI命令有关键字的长格式和短格式。短格式通常是长格式的前几个大写字母。
*   长格式: `:SYSTem:PRESet`
*   短格式: `:SYST:PRES`
*   **注意:** 必须使用完整的长格式或短格式，`SYSTe:PRESe` 是非法的。

### 1.3 消息类型 (Message Type)
SCPI 有两种消息类型：**程序消息 (program)** 和 **响应消息 (response)**。

*   **程序消息**: 从控制器发送到仪器，请求执行某个动作。
*   **响应消息**: 由仪器响应一个查询 (query) 命令而发送给控制器。

#### SCPI 消息结构
*   **消息单元 (Message Unit)**: 最简单的SCPI命令，由命令头（关键字）和消息终止符组成，可能包含参数。
*   **命令头 (Headers)**: 指示仪器执行的指令，有长短格式之分。
*   **查询指示符 (Query Indicator)**: 在命令头后加一个问号 `?`，将其变为查询命令。示例: `VOLTage?`
*   **消息单元分隔符 (Separator)**: 分号 `;` 用于在一条消息中分隔多个命令。
*   **根指示符 (Root Specifier)**: 冒号 `:` 用于将命令路径返回到根节点。
*   **消息终止符 (Terminator)**: 表示消息结束，通常是换行符 `<NL>` (ASCII 10)。

### 1.4 响应数据类型 (Response Data Type)
*   `<CRD>`: 字符响应数据。
*   `<AARD>`: 任意ASCII响应数据。
*   `<SRD>`: 字符串响应数据，用双引号括起来。
*   `<Block>`: 任意块数据。

### 1.5 命令格式 (Command Format)
*   `[]` (方括号): 表示内部的元素是可选的。
*   `{}` (花括号): 表示参数选项，需要从中选择一个。
*   `|` (竖线): 分隔多个参数选项。
*   `<>` (尖括号): 表示必须由一个值替换的参数。

### 1.6 数据类型 (Data Type)
*   **数值参数**:
    *   `<NR1>`: 整数，如 `273`。
    *   `<NR2>`: 浮点数，如 `.273`。
    *   `<NR3>`: 科学记数法，如 `2.73E+2`。
    *   `<Nrf+>`: 扩展的十进制形式，包括上述所有格式以及 `MIN`, `MAX`, `DEF`。
*   **离散参数**: 有限个编程值，如 `IMMediate`, `EXTernal`。
*   **布尔参数**: `ON` 或 `1` (真)，`OFF` 或 `0` (假)。
*   **字符串参数**: 用单引号或双引号括起来的ASCII字符序列。

---

## 第3章 [SOURce] 子系统 (PDF: 35-92)

该子系统包含设置电源输出参数（电压、电流等）的命令。

### `[SOURce:]FUNCtion <CPD>`
*   **功能:** 设置电源的工作模式。
*   **参数:**
    *   `VOLTage`: 恒压 (CV) 优先模式。
    *   `CURRent`: 恒流 (CC) 优先模式。
*   **默认值:** `VOLTage`
*   **示例:** `FUNC CURR` (设置为CC模式)
*   **参考:** PDF 第35页

### `[SOURce:]CURRent[:LEVel][:IMMediate][:AMPLitude] <NRf+>`
*   **功能:** 在CC优先模式下，设置输出电流值 (Iset)。
*   **参数 `<NRf+>`:**
    *   `<value>`: 电流值 (单位: 安培)。
    *   `MINimum | MAXimum | DEFault`
*   **范围:** MIN to MAX
*   **默认值:** 额定电流的 1%
*   **示例:** `CURR 1.5` (设置电流为 1.5A)
*   **参考:** PDF 第38页

### `[SOURce:]CURRent[:LEVel][:IMMediate][:AMPLitude]? [MIN|MAX|DEF]`
*   **功能:** 查询CC优先模式下的输出电流设定值。
*   **示例:** `CURR?` (查询当前设定值), `CURR? MAX` (查询可设定的最大值)
*   **参考:** PDF 第39页

### `[SOURce:]VOLTage[:LEVel][:IMMediate][:AMPLitude] <NRf+>`
*   **功能:** 在CV优先模式下，设置输出电压值 (Vset)。
*   **参数 `<NRf+>`:**
    *   `<value>`: 电压值 (单位: 伏特)。
    *   `MINimum | MAXimum | DEFault`
*   **范围:** MIN to MAX
*   **示例:** `VOLT 12.0` (设置电压为 12.0V)
*   **参考:** PDF 第57页

### `[SOURce:]VOLTage[:LEVel][:IMMediate][:AMPLitude]? [MIN|MAX|DEF]`
*   **功能:** 查询CV优先模式下的输出电压设定值。
*   **示例:** `VOLT?` (查询当前设定值)
*   **参考:** PDF 第58页

### `[SOURce:]POWer:LIMit[:POSitive][:IMMediate][:AMPLitude] <NRf+>`
*   **功能:** 设置功率上限值 (Plim)。
*   **参数 `<NRf+>`:**
    *   `<value>`: 功率值 (单位: 瓦特)。
*   **默认值:** 仪器的额定功率
*   **示例:** `POW:LIM 100` (设置功率上限为 100W)
*   **参考:** PDF 第76-77页

### `[SOURce:]POWer[:OVER]:PROTection:STATe <Bool>`
*   **功能:** 启用或禁用过功率保护 (OPP)。
*   **参数:** `ON | 1` 或 `OFF | 0`
*   **默认值:** `OFF`
*   **示例:** `POW:PROT:STAT ON` (开启过功率保护)
*   **参考:** PDF 第78页

---

## 第4章 OUTPut 子系统 (PDF: 93-101)

该子系统控制电源的输出状态、保护功能等。

### `OUTPut[:STATe] <CPD>`
*   **功能:** 启用或禁用电源输出。
*   **参数 `<CPD>`:**
    *   `ON` 或 `1`
    *   `OFF` 或 `0`
*   **默认值:** `OFF`
*   **示例:** `OUTP ON` (打开输出)
*   **参考:** PDF 第93页

### `OUTPut[:STATe]?`
*   **功能:** 查询当前输出状态 (启用或禁用)。
*   **返回值:** `1` (ON) 或 `0` (OFF)
*   **示例:** `OUTP?`
*   **参考:** PDF 第94页

### `OUTPut:PROTection:CLEar`
*   **功能:** 清除保护状态。如果设备因保护（如OVP, OCP）而关闭输出，此命令可清除锁定状态。
*   **示例:** `OUTP:PROT:CLE`
*   **参考:** PDF 第99页

---

## 第12章 MEASure 子系统 (PDF: 200-212)

该子系统的命令会触发一次新的数据采集并返回测量结果。

### `MEASure[:SCALar]:CURRent[:DC]?`
*   **功能:** 触发并返回平均电流测量值。
*   **返回值:** `<NRf+>` (浮点数值，单位: 安培)
*   **示例:** `MEAS:CURR?`
*   **参考:** PDF 第200页

### `MEASure[:SCALar]:VOLTage[:DC]?`
*   **功能:** 触发并返回平均电压测量值。
*   **返回值:** `<NRf+>` (浮点数值，单位: 伏特)
*   **示例:** `MEAS:VOLT?`
*   **参考:** PDF 第204页

### `MEASure[:SCALar]:POWer[:DC]?`
*   **功能:** 触发并返回平均功率测量值。
*   **返回值:** `<NRf+>` (浮点数值，单位: 瓦特)
*   **示例:** `MEAS:POW?`
*   **参考:** PDF 第207页

### `MEASure[:SCALar]?`
*   **功能:** 用于测量多种数据：电压、电流、功率。
*   **返回值:** `<NRf+>`
*   **示例:** `MEAS?`
*   **参考:** PDF 第212页

---

## 第16章 IEEE-488 通用命令 (PDF: 277-293)

这些是标准化的命令，用于控制仪器的通用功能。

### `*CLS` (Clear Status)
*   **功能:** 清除所有事件寄存器、状态字节和错误队列。
*   **参考:** PDF 第277页

### `*ESE <NR1>` (Event Status Enable)
*   **功能:** 设置标准事件状态使能寄存器。
*   **参考:** PDF 第278页

### `*ESR?` (Event Status Register Query)
*   **功能:** 查询并清除标准事件状态寄存器。
*   **参考:** PDF 第279页

### `*IDN?` (Identification Query)
*   **功能:** 查询仪器的身份识别字符串。
*   **返回值:** 一个包含四个字段的字符串：`制造商, 型号, 序列号, 固件版本`。
*   **示例:** `*IDN?`
*   **参考:** PDF 第280页

### `*OPC` (Operation Complete)
*   **功能:** 当所有挂起的操作完成后，在标准事件寄存器中设置 OPC 位。
*   **参考:** PDF 第281页

### `*OPC?`
*   **功能:** 当所有挂起的操作完成后，向输出缓冲区返回一个 `1`。此命令会阻塞后续命令直到操作完成。
*   **参考:** PDF 第282页

### `*RST` (Reset)
*   **功能:** 将仪器重置为预定义的默认状态。具体哪些参数会被重置，请参考手册中的详细列表。
*   **参考:** PDF 第283-284页

### `*SRE <NR1>` (Service Request Enable)
*   **功能:** 设置服务请求使能寄存器的值。
*   **参考:** PDF 第285页

### `*STB?` (Status Byte Query)
*   **功能:** 查询状态字节寄存器。
*   **参考:** PDF 第287页

### `*TRG` (Trigger)
*   **功能:** 当触发子系统源被设置为 `BUS` 时，产生一个触发信号。
*   **参考:** PDF 第288页

### `*TST?` (Self-Test Query)
*   **功能:** 执行仪器自检并返回结果。
*   **返回值:** `0` (通过) 或 `+1` (失败)。
*   **参考:** PDF 第290页

### `*WAI` (Wait-to-Continue)
*   **功能:** 暂停处理后续命令，直到所有挂起的命令都已完成。
*   **参考:** PDF 第291页

---

## 第19章 错误信息 (PDF: 299-305)

当仪器检测到错误时，可以通过 `SYSTem:ERRor?` 命令查询错误代码和信息。

### 查询错误
*   **命令:** `SYST:ERR?`
*   **正常响应:** `0,"No error"`
*   **错误响应:** `错误代码,"错误信息"`

### 错误代码表 (部分)

| 错误代码 | 错误信息 (Error Message)                          | 描述 (Description)                                     |
| :------- | :------------------------------------------------ | :----------------------------------------------------- |
| **参数设置错误** |
| 101      | DESIGN ERROR: Too many numeric suffices...        | 命令的数字参数数量超出限制。                           |
| 110      | No Input Command to parse                         | 未输入命令（例如发送了空命令）。                       |
| 114      | Numeric suffix is invalid value                   | 发送的数字与命令规范不匹配。                           |
| 116      | Invalid value in numeric or channel list...       | 值或列表参数无效，例如超出范围。                       |
| 120      | Parameter of type Numeric Value overflowed...     | 数字参数溢出，例如设置值不在可设范围内。               |
| 130      | Wrong units for parameter                         | 参数的单位不正确。                                     |
| 140      | Wrong type of parameter(s)                        | 参数的类型不正确。                                     |
| 150      | Wrong number of parameters                        | 参数数量不正确。                                       |
| **命令执行错误** |
| -200     | Execution error [generic]                         | 通用执行错误，通常是命令的状态或设置不正确。           |
| -221     | Settings conflict [check current device state]    | 由于当前仪器状态，数据元素无法执行。                   |
| -222     | Data out of range...                              | 数据元素因数值超出有效范围而无法执行。                 |
| -225     | Out of memory                                     | 设备内存不足，无法执行请求的操作。                     |
| **查询错误** |
| -400     | Query error [generic]                             | 通用查询错误。                                         |
| -410     | Query INTERRUPTED                                 | 查询被中断。                                           |
| -430     | Query DEADLOCKED                                  | 查询死锁。                                             |
| **设备相关错误** |
| 603      | FETCH of data that was not acquired               | 尝试获取（FETCH）尚未采集的数据。                      |
| 604      | Measurement overrange                             | 超出测量范围。                                         |
| 605      | Command not allowed while list initiated          | 当列表（List）已启动时，不允许执行该命令。             |

***
