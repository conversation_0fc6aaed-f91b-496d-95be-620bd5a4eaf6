using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using System.Windows;
using PEMTestSystem.Data;
using PEMTestSystem.Models;
using PEMTestSystem.Services;
using Serilog;

namespace PEMTestSystem.Services
{
    public class AlarmService
    {
        private readonly ILogger _logger;
        private readonly PEMTestDbContext _dbContext;

        public AlarmService(ILogger logger, PEMTestDbContext dbContext)
        {
            _logger = logger;
            _dbContext = dbContext;
        }

        public void Record(string type, string message, AlarmLevel level)
        {
            // 记录日志
            LogAlarm(type, message, level);

            // 显示消息框
            if (level >= AlarmLevel.Info)
            {
                ShowAlarmMessage(type, message, level);
            }

            // 数据库记录
            if (level >= AlarmLevel.Warning)
            {
                SaveToDatabase(type, message, level);
            }
        }

        private void LogAlarm(string type, string message, AlarmLevel level)
        {
            switch (level)
            {
                case AlarmLevel.Debug:
                    _logger.Debug($"[{type}] {message}");
                    break;
                case AlarmLevel.Info:
                    _logger.Information($"[{type}] {message}");
                    break;
                case AlarmLevel.Warning:
                    _logger.Warning($"[{type}] {message}");
                    break;
                case AlarmLevel.Error:
                    _logger.Error($"[{type}] {message}");
                    break;
                case AlarmLevel.Fatal:
                    _logger.Fatal($"[{type}] {message}");
                    break;
            }
        }

        private void ShowAlarmMessage(string type, string message, AlarmLevel level)
        {
            Application.Current.Dispatcher.Invoke(() =>
            {
                var icon = level switch
                {
                    AlarmLevel.Info => MessageBoxImage.Information,
                    AlarmLevel.Warning => MessageBoxImage.Warning,
                    AlarmLevel.Error => MessageBoxImage.Error,
                    AlarmLevel.Fatal => MessageBoxImage.Error,
                    _ => MessageBoxImage.None
                };
                
                MessageBox.Show($"[{type}] {message}", "系统提示", MessageBoxButton.OK, icon);
            });
        }

        private void SaveToDatabase(string type, string message, AlarmLevel level)
        {
            try
            {
                var alarm = new AlarmRecord
                {
                    Type = type,
                    Message = message,
                    Level = level,
                    CreateTime = DateTime.Now,
                    IsConfirmed = false
                };

                _dbContext.AlarmRecords.Add(alarm);
                _dbContext.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "保存警告记录到数据库失败");
            }
        }

        public async Task ConfirmAlarm(int alarmId, string userName)
        {
            var alarm = await _dbContext.AlarmRecords.FindAsync(alarmId);
            if (alarm != null)
            {
                alarm.IsConfirmed = true;
                alarm.ConfirmTime = DateTime.Now;
                alarm.ConfirmUser = userName;
                await _dbContext.SaveChangesAsync();
            }
        }

        public async Task<List<AlarmRecord>> GetUnconfirmedAlarms()
        {
            return await _dbContext.AlarmRecords
                .Where(a => !a.IsConfirmed)
                .OrderByDescending(a => a.CreateTime)
                .ToListAsync();
        }

        public async Task<List<AlarmRecord>> GetAlarmHistory(DateTime? startTime = null, DateTime? endTime = null)
        {
            var query = _dbContext.AlarmRecords.AsQueryable();
            
            if (startTime.HasValue)
                query = query.Where(a => a.CreateTime >= startTime.Value);
                
            if (endTime.HasValue)
                query = query.Where(a => a.CreateTime <= endTime.Value);

            return await query
                .OrderByDescending(a => a.CreateTime)
                .ToListAsync();
        }

        public void Debug(string type, string message)
        {
            Record(type, message, AlarmLevel.Debug);
        }

        public void Info(string type, string message)
        {
            Record(type, message, AlarmLevel.Info); 
        }

        public void Warning(string type, string message)
        {
            Record(type, message, AlarmLevel.Warning);
        }

        public void Error(string type, string message)
        {
            Record(type, message, AlarmLevel.Error);
        }

        public void Fatal(string type, string message)
        {
            Record(type, message, AlarmLevel.Fatal);
        }

        public void Error(string type, string message, Exception ex)
        {
            Record(type, $"{message} 异常信息: {ex.Message}", AlarmLevel.Error);
        }

        public void Fatal(string type, string message, Exception ex)
        {
            Record(type, $"{message} 异常信息: {ex.Message}", AlarmLevel.Fatal);
        }
    }
}