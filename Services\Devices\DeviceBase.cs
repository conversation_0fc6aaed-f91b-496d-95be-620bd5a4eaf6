using System;
using System.Threading.Tasks;
using PEMTestSystem.Models.Devices;

namespace PEMTestSystem.Services.Devices
{
    /// <summary>
    /// 设备基类
    /// 提供设备的通用功能实现
    /// </summary>
    public abstract class DeviceBase : IDevice
    {

        private DeviceStatus _status = DeviceStatus.Disconnected;
        private bool _disposed = false;

        protected DeviceBase(
            string deviceId,
            DeviceType type,
            string deviceName,
            string model)
        {
            DeviceId = deviceId ?? throw new ArgumentNullException(nameof(deviceId));
            Type = type;
            DeviceName = deviceName ?? throw new ArgumentNullException(nameof(deviceName));
            Model = model ?? throw new ArgumentNullException(nameof(model));

            LastCommunicationTime = DateTime.MinValue;
        }

        #region IDevice 属性实现

        public string DeviceId { get; }
        public DeviceType Type { get; }
        public string DeviceName { get; }
        public string Model { get; }
        public DateTime LastCommunicationTime { get; protected set; }

        public DeviceStatus Status
        {
            get => _status;
            protected set
            {
                if (_status != value)
                {
                    var oldStatus = _status;
                    _status = value;
                    OnStatusChanged(oldStatus, value);
                }
            }
        }

        public bool IsConnected => Status == DeviceStatus.Connected || Status == DeviceStatus.Running;

        #endregion

        #region 事件

        public event EventHandler<DeviceStatusChangedEventArgs>? StatusChanged;

        protected virtual void OnStatusChanged(DeviceStatus oldStatus, DeviceStatus newStatus, string? message = null)
        {
            var args = new DeviceStatusChangedEventArgs(DeviceId, oldStatus, newStatus, message);
            StatusChanged?.Invoke(this, args);
            
            App.AlarmService.Info("设备状态", $"设备 {DeviceName} 状态变更: {oldStatus} -> {newStatus}");
        }

        #endregion

        #region 抽象方法

        /// <summary>
        /// 具体的连接实现
        /// </summary>
        protected abstract Task<bool> ConnectInternalAsync();

        /// <summary>
        /// 具体的断开连接实现
        /// </summary>
        protected abstract Task<bool> DisconnectInternalAsync();

        /// <summary>
        /// 具体的连接状态检查实现
        /// </summary>
        protected abstract Task<bool> IsConnectedInternalAsync();

        #endregion

        #region IDevice 方法实现

        public virtual async Task<bool> ConnectAsync()
        {
            if (IsConnected)
            {
                App.AlarmService.Warning("设备连接", $"设备 {DeviceName} 已经连接");
                return true;
            }

            try
            {
                Status = DeviceStatus.Connecting;
                App.AlarmService.Info("设备连接", $"正在连接设备 {DeviceName}");

                var result = await ConnectInternalAsync();
                
                if (result)
                {
                    Status = DeviceStatus.Connected;
                    LastCommunicationTime = DateTime.Now;
                    App.AlarmService.Info("设备连接", $"设备 {DeviceName} 连接成功");
                }
                else
                {
                    Status = DeviceStatus.Error;
                    App.AlarmService.Error("设备连接", $"设备 {DeviceName} 连接失败");
                }

                return result;
            }
            catch (Exception ex)
            {
                Status = DeviceStatus.Error;
                App.AlarmService.Error("设备连接", $"设备 {DeviceName} 连接异常", ex);
                return false;
            }
        }

        public virtual async Task<bool> DisconnectAsync()
        {
            try
            {
                var result = await DisconnectInternalAsync();
                
                if (result)
                {
                    Status = DeviceStatus.Disconnected;
                    App.AlarmService.Info("设备连接", $"设备 {DeviceName} 断开连接成功");
                }
                else
                {
                    App.AlarmService.Warning("设备连接", $"设备 {DeviceName} 断开连接失败");
                }

                return result;
            }
            catch (Exception ex)
            {
                Status = DeviceStatus.Error;
                App.AlarmService.Error("设备连接", $"设备 {DeviceName} 断开连接异常", ex);
                return false;
            }
        }

        public virtual async Task<bool> IsConnectedAsync()
        {
            try
            {
                var result = await IsConnectedInternalAsync();
                
                if (result)
                {
                    LastCommunicationTime = DateTime.Now;
                }
                else if (IsConnected)
                {
                    Status = DeviceStatus.Disconnected;
                    App.AlarmService.Warning("设备状态", $"设备 {DeviceName} 连接丢失");
                }

                return result;
            }
            catch (Exception ex)
            {
                Status = DeviceStatus.Error;
                App.AlarmService.Error("设备状态", $"设备 {DeviceName} 状态检查异常", ex);
                return false;
            }
        }

        public virtual async Task<bool> InitializeAsync()
        {
            try
            {
                App.AlarmService.Info("设备初始化", $"正在初始化设备 {DeviceName}");
                
                // 基础初始化逻辑
                if (!IsConnected)
                {
                    var connected = await ConnectAsync();
                    if (!connected)
                    {
                        return false;
                    }
                }

                // 子类可以重写此方法添加特定的初始化逻辑
                var result = await InitializeInternalAsync();
                
                if (result)
                {
                    App.AlarmService.Info("设备初始化", $"设备 {DeviceName} 初始化成功");
                }
                else
                {
                    App.AlarmService.Error("设备初始化", $"设备 {DeviceName} 初始化失败");
                }

                return result;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备初始化", $"设备 {DeviceName} 初始化异常", ex);
                return false;
            }
        }

        public virtual async Task<bool> ResetAsync()
        {
            try
            {
                App.AlarmService.Info("设备重置", $"正在重置设备 {DeviceName}");
                
                var result = await ResetInternalAsync();
                
                if (result)
                {
                    App.AlarmService.Info("设备重置", $"设备 {DeviceName} 重置成功");
                }
                else
                {
                    App.AlarmService.Error("设备重置", $"设备 {DeviceName} 重置失败");
                }

                return result;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备重置", $"设备 {DeviceName} 重置异常", ex);
                return false;
            }
        }

        public virtual async Task<DeviceInfo> GetDeviceInfoAsync()
        {
            try
            {
                var info = await GetDeviceInfoInternalAsync();
                LastCommunicationTime = DateTime.Now;
                return info;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("设备信息", $"获取设备 {DeviceName} 信息异常", ex);
                throw;
            }
        }

        #endregion

        #region 虚拟方法（子类可重写）

        /// <summary>
        /// 内部初始化实现
        /// </summary>
        protected virtual Task<bool> InitializeInternalAsync()
        {
            return Task.FromResult(true);
        }

        /// <summary>
        /// 内部重置实现
        /// </summary>
        protected virtual Task<bool> ResetInternalAsync()
        {
            return Task.FromResult(true);
        }

        /// <summary>
        /// 内部获取设备信息实现
        /// </summary>
        protected virtual Task<DeviceInfo> GetDeviceInfoInternalAsync()
        {
            return Task.FromResult(new DeviceInfo
            {
                DeviceId = DeviceId,
                DeviceName = DeviceName,
                Model = Model,
                Version = "Unknown",
                SerialNumber = "Unknown"
            });
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 更新最后通信时间
        /// </summary>
        protected void UpdateLastCommunicationTime()
        {
            LastCommunicationTime = DateTime.Now;
        }

        /// <summary>
        /// 验证参数范围
        /// </summary>
        protected static void ValidateRange(double value, double min, double max, string parameterName)
        {
            if (value < min || value > max)
            {
                throw new ArgumentOutOfRangeException(parameterName, 
                    $"{parameterName} 必须在 {min} 到 {max} 之间，当前值: {value}");
            }
        }

        #endregion

        #region IDisposable 实现

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    try
                    {
                        DisconnectAsync().Wait(5000);
                    }
                    catch (Exception ex)
                    {
                        App.AlarmService.Error("设备资源", $"释放设备 {DeviceName} 资源时发生错误", ex);
                    }
                }

                _disposed = true;
            }
        }

        #endregion
    }
}
