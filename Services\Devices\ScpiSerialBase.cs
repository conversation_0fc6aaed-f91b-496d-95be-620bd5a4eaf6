using PEMTestSystem.Models.Devices;
using System;
using System.IO.Ports;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace PEMTestSystem.Services.Devices
{
    /// <summary>
    /// SCPI串口通信基类
    /// 实现SCPI协议的串口通信功能
    /// </summary>
    public abstract class ScpiSerialBase : DeviceBase
    {
        protected readonly SerialPort _serialPort;
        protected readonly SemaphoreSlim _communicationLock;
        protected readonly int _timeout;
        protected readonly int _retryCount;

        // SCPI通信参数
        protected const string COMMAND_TERMINATOR = "\n";
        protected const int DEFAULT_TIMEOUT = 3000;
        protected const int DEFAULT_RETRY_COUNT = 3;
        protected const int RESPONSE_DELAY = 50; // 响应延迟（毫秒）

        protected ScpiSerialBase(
            string portName,
            int baudRate,
            string deviceId,
            DeviceType deviceType,
            string deviceName,
            string model,
            int timeout = DEFAULT_TIMEOUT,
            int retryCount = DEFAULT_RETRY_COUNT)
            : base(deviceId, deviceType, deviceName, model)
        {
            _timeout = timeout;
            _retryCount = retryCount;
            _communicationLock = new SemaphoreSlim(1, 1);

            // 配置串口参数
            _serialPort = new SerialPort
            {
                PortName = portName,
                BaudRate = baudRate,
                DataBits = 8,
                Parity = Parity.None,
                StopBits = StopBits.One,
                Handshake = Handshake.None,
                ReadTimeout = timeout,
                WriteTimeout = timeout,
                Encoding = Encoding.ASCII
            };

            App.AlarmService.Debug("SCPI设备", $"SCPI设备 {DeviceId} 初始化完成，端口: {portName}, 波特率: {baudRate}");
        }

        /// <summary>
        /// 连接到设备
        /// </summary>
        protected override async Task<bool> ConnectInternalAsync()
        {
            try
            {
                if (_serialPort.IsOpen)
                {
                    App.AlarmService.Warning("SCPI设备", $"串口 {_serialPort.PortName} 已经打开");
                    return true;
                }

                _serialPort.Open();
                
                // 等待串口稳定
                await Task.Delay(100);

                // 清空缓冲区
                _serialPort.DiscardInBuffer();
                _serialPort.DiscardOutBuffer();

                // 测试通信 - 发送设备识别命令
                var idnResponse = await SendQueryAsync("*IDN?");
                if (string.IsNullOrEmpty(idnResponse))
                {
                    App.AlarmService.Error("SCPI设备", $"设备 {DeviceId} 通信测试失败");
                    _serialPort.Close();
                    return false;
                }

                App.AlarmService.Info("SCPI设备", $"设备 {DeviceId} 连接成功，设备信息: {idnResponse}");
                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("SCPI设备", $"设备 {DeviceId} 连接失败", ex);
                if (_serialPort.IsOpen)
                {
                    _serialPort.Close();
                }
                return false;
            }
        }

        /// <summary>
        /// 断开设备连接
        /// </summary>
        protected override async Task<bool> DisconnectInternalAsync()
        {
            try
            {
                if (_serialPort.IsOpen)
                {
                    _serialPort.Close();
                }
                
                await Task.Delay(100); // 等待端口完全关闭
                App.AlarmService.Info("SCPI设备", $"设备 {DeviceId} 断开连接");
                return true;
            }
            catch (Exception ex)
            {
                App.AlarmService.Error("SCPI设备", $"设备 {DeviceId} 断开连接失败", ex);
                return false;
            }
        }

        /// <summary>
        /// 发送SCPI命令（无返回值）
        /// </summary>
        /// <param name="command">SCPI命令</param>
        /// <returns>发送是否成功</returns>
        protected async Task<bool> SendCommandAsync(string command)
        {
            if (!IsConnected)
            {
                App.AlarmService.Warning("SCPI设备", $"设备 {DeviceId} 未连接，无法发送命令: {command}");
                return false;
            }

            await _communicationLock.WaitAsync();
            try
            {
                for (int attempt = 1; attempt <= _retryCount; attempt++)
                {
                    try
                    {
                        // 清空输入缓冲区
                        _serialPort.DiscardInBuffer();

                        // 发送命令
                        var commandWithTerminator = command + COMMAND_TERMINATOR;
                        _serialPort.Write(commandWithTerminator);

                        App.AlarmService.Debug("SCPI设备", $"设备 {DeviceId} 发送命令: {command} (尝试 {attempt}/{_retryCount})");

                        LastCommunicationTime = DateTime.Now;
                        return true;
                    }
                    catch (Exception ex)
                    {
                        App.AlarmService.Warning("SCPI设备", $"设备 {DeviceId} 发送命令失败 (尝试 {attempt}/{_retryCount}): {command} - {ex.Message}");

                        if (attempt < _retryCount)
                        {
                            await Task.Delay(100); // 重试前等待
                        }
                    }
                }

                App.AlarmService.Error("SCPI设备", $"设备 {DeviceId} 发送命令最终失败: {command}");
                return false;
            }
            finally
            {
                _communicationLock.Release();
            }
        }

        /// <summary>
        /// 发送SCPI查询命令并获取响应
        /// </summary>
        /// <param name="query">SCPI查询命令</param>
        /// <returns>设备响应，失败时返回null</returns>
        protected async Task<string?> SendQueryAsync(string query)
        {
            if (!IsConnected)
            {
                App.AlarmService.Warning("SCPI设备", $"设备 {DeviceId} 未连接，无法发送查询: {query}");
                return null;
            }

            await _communicationLock.WaitAsync();
            try
            {
                for (int attempt = 1; attempt <= _retryCount; attempt++)
                {
                    try
                    {
                        // 清空缓冲区
                        _serialPort.DiscardInBuffer();
                        _serialPort.DiscardOutBuffer();

                        // 发送查询命令
                        var queryWithTerminator = query + COMMAND_TERMINATOR;
                        _serialPort.Write(queryWithTerminator);

                        // 等待响应
                        await Task.Delay(RESPONSE_DELAY);

                        // 读取响应
                        var response = _serialPort.ReadLine().Trim();

                        App.AlarmService.Debug("SCPI设备", $"设备 {DeviceId} 查询成功: {query} -> {response} (尝试 {attempt}/{_retryCount})");

                        LastCommunicationTime = DateTime.Now;
                        return response;
                    }
                    catch (TimeoutException)
                    {
                        App.AlarmService.Warning("SCPI设备", $"设备 {DeviceId} 查询超时 (尝试 {attempt}/{_retryCount}): {query}");
                    }
                    catch (Exception ex)
                    {
                        App.AlarmService.Warning("SCPI设备", $"设备 {DeviceId} 查询失败 (尝试 {attempt}/{_retryCount}): {query} - {ex.Message}");
                    }

                    if (attempt < _retryCount)
                    {
                        await Task.Delay(200); // 重试前等待更长时间
                    }
                }

                App.AlarmService.Error("SCPI设备", $"设备 {DeviceId} 查询最终失败: {query}");
                return null;
            }
            finally
            {
                _communicationLock.Release();
            }
        }

        /// <summary>
        /// 解析数值响应
        /// </summary>
        /// <param name="response">设备响应</param>
        /// <param name="defaultValue">解析失败时的默认值</param>
        /// <returns>解析后的数值</returns>
        protected double ParseNumericResponse(string? response, double defaultValue = 0.0)
        {
            if (string.IsNullOrWhiteSpace(response))
            {
                return defaultValue;
            }

            if (double.TryParse(response.Trim(), out double value))
            {
                return value;
            }

            App.AlarmService.Warning("SCPI设备", $"设备 {DeviceId} 数值响应解析失败: {response}");
            return defaultValue;
        }

        /// <summary>
        /// 解析布尔响应
        /// </summary>
        /// <param name="response">设备响应</param>
        /// <param name="defaultValue">解析失败时的默认值</param>
        /// <returns>解析后的布尔值</returns>
        protected bool ParseBooleanResponse(string? response, bool defaultValue = false)
        {
            if (string.IsNullOrWhiteSpace(response))
            {
                return defaultValue;
            }

            var trimmed = response.Trim();
            return trimmed == "1" || trimmed.Equals("ON", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// 检查设备连接状态
        /// </summary>
        protected override async Task<bool> IsConnectedInternalAsync()
        {
            return await Task.FromResult(_serialPort?.IsOpen == true);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _serialPort?.Close();
                _serialPort?.Dispose();
                _communicationLock?.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
