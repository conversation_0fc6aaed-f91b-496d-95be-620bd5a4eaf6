好的，根据您提供的文档，以下是编写与 TX6 系列温控器进行 Modbus 通讯程序所需的所有相关内容。

### **1. 通讯基础规格**

*   **物理接口**: RS485
*   **协议**: Modbus RTU 模式
*   **波特率**: 可选 2.4K, 4.8K, 9.6K, 19.2K, 28.8K, 38.4K bps (通过仪表参数 `LCY` 设置)
*   **设备地址**: 可通过仪表参数 `Adrs` 设置，范围为 1–100。地址 00H 为广播地址。

### **2. 数据帧格式 (Modbus RTU)**

#### **2.1 字符结构 (Character Frame)**

*   **格式**: 8, N, 1
*   **组成**:
    *   1 位起始位 (Start Bit)
    *   8 位数据位 (Data Bits)
    *   无奇偶校验 (No Parity)
    *   1 位停止位 (Stop Bit)

#### **2.2 讯息帧结构 (Message Frame)**

讯息帧由设备地址、功能码、数据和 CRC 校验码组成。

| 组成部分 | 长度 | 说明 |
| :--- | :--- | :--- |
| **Address** | 8-bit (1字节) | 控制器地址 (1-254) |
| **Function** | 8-bit (1字节) | 功能码，如 03H (读), 06H (写) |
| **Data** | N * 8-bit (N字节) | 数据内容，包含寄存器地址和要读写的数据 |
| **CRC Check** | 16-bit (2字节) | 循环冗余校验码 (低字节在前，高字节在后) |

*   **帧间静默时间**: 指令帧之间必须保持至少 20ms 的无信号间隔。

### **3. 支持的功能码 (Function Code)**

*   **`03H`**: 读出控制器寄存器内容 (Read Holding Registers)。用于读取一个或多个连续的寄存器值。
*   **`06H`**: 写入单个寄存器内容 (Write Single Register)。用于向单个寄存器写入数据。
*   **`08H`**: (根据错误码部分推断) 诊断功能。

### **4. CRC-16 校验码计算**

CRC 校验码是对从设备地址 (Address) 到数据内容 (Data Content) 的所有字节进行计算的结果。

**计算规则:**

1.  **初始化**: 创建一个 16-bit 的寄存器 (CRC 寄存器)，并置为 `FFFFH`。
2.  **首字节异或**: 将讯息帧的第一个字节与 CRC 寄存器的低 8 位进行异或 (XOR) 运算，结果存回 CRC 寄存器。
3.  **移位**: 将 CRC 寄存器向右移动一位，高位补 0。
4.  **检查移出位**:
    *   如果右移前最低位 (LSB) 为 0，则重复步骤 3。
    *   如果右移前最低位 (LSB) 为 1，则将 CRC 寄存器与多项式 `A001H` 进行异或运算。
5.  **重复**: 重复步骤 3 和 4，直到 8 位数据全部处理完毕。
6.  **处理后续字节**: 重复步骤 2 到 5，处理讯息帧中的下一个字节，直到所有字节都处理完毕。
7.  **最终结果**: 计算完成后 CRC 寄存器中的值即为最终的 CRC 校验码。

**注意**: 在将计算出的 CRC 校验码附加到讯息帧末尾时，需要**交换高低字节**的位置，即低字节在前，高字节在后。

### **5. 寄存器地址定义**

以下是部分关键参数的 Modbus 寄存器地址。所有地址均为 16-bit 地址。

| 地址 (HEX) | 地址 (DEC) | 代号 | 说明 | 读/写 |
| :--- | :--- | :--- | :--- | :--- |
| **0000H** | 40001 | PV | 测量值 (Process Value) | R |
| **0001H** | 40002 | SV | 设定值 (Set Value) | R/W |
| **0002H** | 40003 | LED | LED 指示灯状态 | R |
| **0003H** | 40004 | OUTB | 当前输出百分比 | R |
| **0004H** | 40005 | AT/ONOFF | 自整定 (AT) 和加热开关 | R/W |
| **0005H** | 40006 | AL1 | 报警值 1 | R/W |
| **0006H** | 40007 | AL2 | 报警值 2 | R/W |
| **0009H** | 40010 | P | 比例项 (Proportional) | R/W |
| **000AH** | 40011 | I | 积分项 (Integral) | R/W |
| **000BH** | 40012 | D | 微分项 (Derivative) | R/W |
| **000DH** | 40014 | T | 控制周期 | R/W |

**特殊数据说明:**

*   **PV 测量值 (0000H)**: 使用二进制补码表示。
    *   `LLLL` (下溢出) 显示为 `-16666` (BEE6H)。
    *   `HHHH` (上溢出) 显示为 `18888` (49C8H)。
*   **LED 状态 (0002H)**: 这是一个位掩码 (Bitmask)，低字节的每一位代表一个状态指示灯。
    *   **BIT0 (0001)**: OUT1 (主控输出1) 状态
    *   **BIT1 (0002)**: OUT2 (输出2) 状态
    *   **BIT2 (0004)**: AT (自整定) 状态
    *   **BIT3 (0008)**: AL1 (报警1) 状态
    *   **BIT4 (0016)**: AL2 (报警2) 状态
    *   **BIT6 (0064)**: 温标单位 (0=摄氏度, 1=华氏度)
*   **小数点**: LED 状态寄存器 (0002H) 的 BIT8 和 BIT9 用于定义小数点位置 (00=无, 01=1位, 10=2位, 11=3位)。

### **6. 通讯示例**

#### **示例 1: 读取 PV 和 SV 值 (功能码 03H)**

*   **目的**: 从地址为 `01H` 的控制器读取从 `0000H` (PV) 开始的 2 个寄存器。
*   **主机发送 (询问)**:
    *   **Address**: `01H`
    *   **Function**: `03H`
    *   **起始地址**: `0000H`
    *   **数据个数**: `0002H`
    *   **CRC**: `C40B` (低字节 `0B`, 高字节 `C4`) - *注意：此CRC为示例，实际值需根据前序字节计算*
*   **控制器回应**:
    *   **Address**: `01H`
    *   **Function**: `03H`
    *   **返回字节数**: `04H` (2个寄存器 x 2字节/寄存器)
    *   **PV 值**: `00AAH` (示例值, 170)
    *   **SV 值**: `00C8H` (示例值, 200)
    *   **CRC**: `F8E4` (低字节 `E4`, 高字节 `F8`) - *注意：此CRC为示例*

#### **示例 2: 设定 SV 值 (功能码 06H)**

*   **目的**: 向地址为 `01H` 的控制器 `0001H` (SV) 寄存器写入值 `200` (`00C8H`)。
*   **主机发送 (询问)**:
    *   **Address**: `01H`
    *   **Function**: `06H`
    *   **寄存器地址**: `0001H`
    *   **写入数值**: `00C8H`
    *   **CRC**: `D99CH` (文档示例)
*   **控制器回应 (正常时)**:
    *   控制器将原样返回主机的询问讯息作为确认。

### **7. 错误处理**

如果通讯出错，控制器将返回一个错误码。错误帧的**功能码**会是原功能码的最高位置 1 (即 `原功能码 + 80H`)。

*   **错误回应帧格式**:
    *   **Address**: `01H`
    *   **Function**: `8XH` (例如，读取错误为 `83H`，写入错误为 `86H`)
    *   **Exception Code**: 错误码 (见下表)
    *   **CRC**: 新计算的 CRC 值

*   **错误码 (Exception Code) 意义**:

| 错误码 | 说明 |
| :--- | :--- |
| **01** | **功能码错误**: 控制器不支持该功能码。 |
| **02** | **资料位址错误**: 请求的寄存器地址不存在或超出范围。 |
| **03** | **资料内容值错误**: 写入的数据值太大或太小，超出参数允许范围。 |
| **04** | **控制器无法处理**: 控制器执行命令失败。 |
| **06** | **控制器忙线中**: 控制器正在处理其他任务，暂时无法回应。 |
| **09** | **检查码错误**: 主机发送的 CRC 校验码错误。 |
| **11** | **Frame error**: 字符帧错误 (例如波特率或数据位不匹配)。 |
| **12** | **讯息字节太短**: 指令帧的长度不足。 |
| **13** | **讯息字节太长**: 指令帧的长度过长。 |