{"format": 1, "restore": {"C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\PEM电解槽自动化测试系统.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\PEM电解槽自动化测试系统.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\PEM电解槽自动化测试系统.csproj", "projectName": "PEM电解槽自动化测试系统", "projectPath": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\PEM电解槽自动化测试系统.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Nutstore\\1\\Project\\3制氢测试系统\\program\\PEM电解槽自动化测试系统\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.18, 8.0.18]"}, {"name": "Microsoft.Net.Sdk.Compilers.Toolset", "version": "[9.0.302, 9.0.302]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[8.0.18, 8.0.18]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.18, 8.0.18]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.18, 8.0.18]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}